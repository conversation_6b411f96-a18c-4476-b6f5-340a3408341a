#!/bin/bash

# Arien AI CLI Demo Script
# Demonstrates all implemented features and capabilities

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    ARIEN AI CLI DEMO                        ║"
    echo "║              Comprehensive Feature Showcase                 ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_section() {
    echo -e "\n${PURPLE}=== $1 ===${NC}\n"
}

print_step() {
    echo -e "${BLUE}➤ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# Check if Arien AI is installed
check_installation() {
    print_section "Installation Check"
    
    if command -v arien >/dev/null 2>&1; then
        print_success "Arien AI CLI is installed"
        echo "Version: $(arien --version 2>/dev/null || echo 'Unknown')"
    else
        print_info "Arien AI CLI not found in PATH"
        print_step "Checking local installation..."
        
        if [[ -f "./dist/index.js" ]]; then
            print_success "Found local build"
            alias arien="node ./dist/index.js"
        else
            echo -e "${RED}Error: Arien AI CLI not found. Please install first.${NC}"
            echo "Run: ./install.sh install"
            exit 1
        fi
    fi
}

# Demonstrate build and test system
demo_build_system() {
    print_section "Build System & Testing"
    
    print_step "Running TypeScript build..."
    npm run build
    print_success "Build completed successfully"
    
    print_step "Running comprehensive test suite..."
    npm test -- --run
    print_success "All tests passed"
    
    print_step "Running type checking..."
    npm run typecheck
    print_success "Type checking passed"
    
    print_step "Running linting..."
    npm run lint
    print_success "Linting passed"
}

# Demonstrate CLI commands
demo_cli_commands() {
    print_section "CLI Commands"
    
    print_step "Showing help information..."
    arien --help
    
    print_step "Listing available tools..."
    arien tools --list
    
    print_step "Showing tool documentation..."
    arien tools --docs bash
    
    print_step "Checking configuration..."
    arien config --list 2>/dev/null || print_info "No configuration found (expected for fresh install)"
    
    print_step "Listing providers..."
    arien providers --list 2>/dev/null || print_info "No providers configured yet"
}

# Demonstrate tool system
demo_tool_system() {
    print_section "Tool System Demonstration"
    
    print_step "Testing bash tool..."
    echo '{"command": "echo Hello from Arien AI"}' | node -e "
        const { ToolManager } = require('./dist/core/tools/index.js');
        const toolManager = new ToolManager();
        const input = JSON.parse(require('fs').readFileSync(0, 'utf8'));
        toolManager.executeTool('bash', input).then(result => {
            console.log('Result:', result.success ? 'SUCCESS' : 'FAILED');
            console.log('Output:', result.output || result.error);
        });
    "
    
    print_step "Testing glob tool..."
    echo '{"pattern": "*.json", "path": "."}' | node -e "
        const { ToolManager } = require('./dist/core/tools/index.js');
        const toolManager = new ToolManager();
        const input = JSON.parse(require('fs').readFileSync(0, 'utf8'));
        toolManager.executeTool('glob', input).then(result => {
            console.log('Found files:', result.success ? 'SUCCESS' : 'FAILED');
            if (result.result) console.log('Files found:', result.result.files?.length || 0);
        });
    "
    
    print_step "Testing write tool..."
    echo '{"path": "./demo-test.txt", "content": "This is a test file created by Arien AI demo"}' | node -e "
        const { ToolManager } = require('./dist/core/tools/index.js');
        const toolManager = new ToolManager();
        const input = JSON.parse(require('fs').readFileSync(0, 'utf8'));
        toolManager.executeTool('write', input).then(result => {
            console.log('File creation:', result.success ? 'SUCCESS' : 'FAILED');
            if (result.success) console.log('Created: demo-test.txt');
        });
    "
    
    # Clean up test file
    rm -f ./demo-test.txt
}

# Demonstrate system prompt and self-learning
demo_system_prompt() {
    print_section "System Prompt & Self-Learning"
    
    print_step "Generating comprehensive system prompt..."
    node -e "
        const { SystemPromptManager } = require('./dist/core/prompts/system-prompt.js');
        const { ToolManager } = require('./dist/core/tools/index.js');
        const toolManager = new ToolManager();
        const promptManager = new SystemPromptManager(toolManager);
        const prompt = promptManager.generateSystemPrompt();
        console.log('System prompt length:', prompt.length, 'characters');
        console.log('Contains autonomous behavior instructions:', prompt.includes('AUTONOMOUS BEHAVIOR') ? 'YES' : 'NO');
        console.log('Contains never give up logic:', prompt.includes('NEVER GIVE UP') ? 'YES' : 'NO');
        console.log('Contains tool documentation:', prompt.includes('AVAILABLE TOOLS') ? 'YES' : 'NO');
    "
    
    print_step "Testing performance metrics recording..."
    node -e "
        const { SystemPromptManager } = require('./dist/core/prompts/system-prompt.js');
        const { ToolManager } = require('./dist/core/tools/index.js');
        const toolManager = new ToolManager();
        const promptManager = new SystemPromptManager(toolManager);
        
        // Record some sample metrics
        promptManager.recordPromptPerformance('demo_task', true, 1500, 0.9);
        promptManager.recordPromptPerformance('demo_task', true, 1200, 0.95);
        promptManager.recordPromptPerformance('demo_task', false, 3000, 0.3);
        
        const report = promptManager.getPerformanceReport();
        console.log('Performance report generated successfully');
        console.log('Report contains metrics:', report.includes('Success Metrics') ? 'YES' : 'NO');
    "
}

# Demonstrate configuration system
demo_configuration() {
    print_section "Configuration System"
    
    print_step "Testing configuration management..."
    node -e "
        const { ConfigManager } = require('./dist/core/config/manager.js');
        const configManager = new ConfigManager();
        
        console.log('Configuration validation:', configManager.validateConfig().isValid ? 'VALID' : 'NEEDS_SETUP');
        
        // Test provider management
        const testConfig = { model: 'demo-model', apiKey: 'demo-key' };
        configManager.saveProvider('demo-provider', testConfig).then(() => {
            const retrieved = configManager.getProvider('demo-provider');
            console.log('Provider save/retrieve:', retrieved ? 'SUCCESS' : 'FAILED');
            
            // Clean up
            configManager.deleteProvider('demo-provider');
            console.log('Provider cleanup: SUCCESS');
        });
    "
}

# Demonstrate error handling and retry logic
demo_error_handling() {
    print_section "Error Handling & Retry Logic"
    
    print_step "Testing error categorization..."
    node -e "
        const { createError, isRetryableError } = require('./dist/utils/errors.js');
        
        const networkError = createError('Connection failed', 'NETWORK_CONNECTION_FAILED', 'network', true);
        const authError = createError('Invalid API key', 'AUTH_INVALID_API_KEY', 'auth', false);
        
        console.log('Network error retryable:', isRetryableError(networkError) ? 'YES' : 'NO');
        console.log('Auth error retryable:', isRetryableError(authError) ? 'YES' : 'NO');
        console.log('Error categorization: SUCCESS');
    "
    
    print_step "Testing retry manager..."
    node -e "
        const { RetryManager } = require('./dist/utils/retry.js');
        const retryManager = new RetryManager({ maxAttempts: 2, baseDelay: 100 });
        
        let attempts = 0;
        retryManager.execute(async () => {
            attempts++;
            if (attempts === 1) throw new Error('Temporary failure');
            return 'Success on retry';
        }).then(result => {
            console.log('Retry logic result:', result);
            console.log('Attempts made:', attempts);
        }).catch(err => {
            console.log('Retry failed:', err.message);
        });
    "
}

# Show installation system features
demo_installation() {
    print_section "Installation System"
    
    print_step "Running health check..."
    if [[ -f "./install.sh" ]]; then
        ./install.sh health
        print_success "Health check completed"
    else
        print_info "install.sh not found, skipping health check"
    fi
    
    print_step "Showing installer help..."
    if [[ -f "./install.sh" ]]; then
        ./install.sh --help
    else
        print_info "install.sh not found"
    fi
}

# Main demo execution
main() {
    print_header
    
    echo -e "${CYAN}This demo showcases all implemented features of the Arien AI CLI system.${NC}"
    echo -e "${CYAN}Each section demonstrates different aspects of the sophisticated AI-powered terminal.${NC}\n"
    
    check_installation
    demo_build_system
    demo_cli_commands
    demo_tool_system
    demo_system_prompt
    demo_configuration
    demo_error_handling
    demo_installation
    
    print_section "Demo Complete"
    print_success "All features demonstrated successfully!"
    echo -e "\n${CYAN}The Arien AI CLI is ready for production use with:${NC}"
    echo -e "${GREEN}✓ Multiple AI providers (DeepSeek, Ollama)${NC}"
    echo -e "${GREEN}✓ Autonomous tool execution with 6 comprehensive tools${NC}"
    echo -e "${GREEN}✓ Real-time streaming with interruption support${NC}"
    echo -e "${GREEN}✓ Self-learning system with prompt evolution${NC}"
    echo -e "${GREEN}✓ Never give up logic with intelligent retry mechanisms${NC}"
    echo -e "${GREEN}✓ Professional CLI interface with slash commands${NC}"
    echo -e "${GREEN}✓ Comprehensive error handling and recovery${NC}"
    echo -e "${GREEN}✓ Universal installation system for all platforms${NC}"
    echo -e "\n${PURPLE}Start using Arien AI: ${YELLOW}arien${NC}"
}

# Run the demo
main "$@"
