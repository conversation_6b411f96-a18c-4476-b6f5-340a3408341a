# Arien AI CLI Tools Documentation

This document provides comprehensive documentation for all available tools in the Arien AI CLI system.

## Overview

The Arien AI CLI includes a sophisticated tool system that allows the AI to autonomously execute various operations based on user requests. Each tool is designed with safety, reliability, and ease of use in mind.

## Tool Categories

### System Tools
Tools for interacting with the operating system and executing commands.

### File Tools
Tools for file system operations, content manipulation, and file management.

### Web Tools
Tools for internet access, web searching, and content retrieval.

### Development Tools
Tools specifically designed for software development workflows.

---

## BASH Tool

**Category:** System  
**Description:** Runs and executes all types of bash commands in the interactive shell

### Primary Use Cases
- Execute system commands and scripts
- File system operations (ls, find, etc.)
- Process management and monitoring
- Environment inspection
- Development tasks (git, npm, etc.)

### When NOT to Use
- For destructive operations without user confirmation
- Commands that require interactive input
- Long-running processes (use with caution due to timeout)
- Commands that modify critical system files

### Parameters
- **command** (required): The bash command to execute
- **workingDirectory** (optional): Working directory for command execution

### Security Considerations
- Commands are validated against dangerous patterns
- 30-second timeout prevents hanging processes
- Output buffer limited to 1MB
- Certain destructive commands are blocked

### Examples
```json
{"command": "ls -la"}
{"command": "git status", "workingDirectory": "/path/to/repo"}
{"command": "npm install", "workingDirectory": "./project"}
```

---

## GREP Tool

**Category:** File  
**Description:** Fast content search tool that finds files containing specific text or patterns

### Primary Use Cases
- Search for specific text across multiple files
- Find code patterns and functions
- Locate configuration values
- Debug by finding error messages or logs
- Code analysis and refactoring assistance

### When NOT to Use
- For binary file searches (use specialized tools)
- When you need complex regex that grep doesn't support well
- For very large codebases where performance is critical
- When you need semantic code search

### Parameters
- **pattern** (required): Text pattern or regex to search for
- **path** (required): File or directory path to search in
- **recursive** (optional): Search recursively in directories (default: true)
- **caseInsensitive** (optional): Perform case-insensitive search (default: false)
- **maxResults** (optional): Maximum number of results to return (default: 50)

### Examples
```json
{"pattern": "function", "path": "./src", "recursive": true}
{"pattern": "TODO", "path": ".", "caseInsensitive": true}
{"pattern": "import.*react", "path": "./components"}
```

---

## GLOB Tool

**Category:** File  
**Description:** Fast file pattern matching tool that finds files by name and pattern

### Primary Use Cases
- Find files by extension (*.ts, *.js, *.json)
- Locate files with specific naming patterns
- Discover configuration files
- Find test files or documentation
- Batch file operations preparation

### Parameters
- **pattern** (required): File pattern to match (supports wildcards)
- **path** (optional): Base directory to search in (default: current directory)
- **maxResults** (optional): Maximum number of results (default: 100)
- **includeDirectories** (optional): Include directories in results (default: false)

### Pattern Syntax Examples
- `*.ts` - All TypeScript files
- `**/*.js` - All JavaScript files recursively
- `test*.json` - JSON files starting with "test"
- `src/**/index.*` - All index files in src directory
- `{*.ts,*.js}` - TypeScript OR JavaScript files

### Examples
```json
{"pattern": "*.ts", "path": "./src"}
{"pattern": "**/*.test.js", "path": "."}
{"pattern": "config*.json", "maxResults": 10}
```

---

## WRITE Tool

**Category:** File  
**Description:** File writing tool that creates or updates files in the filesystem

### Primary Use Cases
- Create new files with content
- Update existing files by overwriting
- Append content to existing files
- Save generated code, configurations, or documentation
- Create log files and data exports

### When NOT to Use
- For complex file edits (use edit tool for precise modifications)
- For binary file operations
- When you need to preserve file permissions/metadata exactly
- For moving or renaming files (use bash tool with mv command)

### Parameters
- **path** (required): File path to write to
- **content** (required): Content to write to the file
- **append** (optional): Append to file instead of overwriting (default: false)
- **createDirectories** (optional): Create parent directories if needed (default: true)
- **backup** (optional): Create backup of existing file (default: false)
- **encoding** (optional): File encoding (default: 'utf8')

### Examples
```json
{"path": "example.txt", "content": "Hello World"}
{"path": "log.txt", "content": "New entry\n", "append": true}
{"path": "config/app.json", "content": "{}", "createDirectories": true}
```

---

## EDIT Tool

**Category:** File  
**Description:** Edits files by replacing text, creating new files, or deleting content

### Primary Use Cases
- Precise text replacements in existing files
- Insert new lines at specific positions
- Delete specific lines from files
- Create new files with initial content
- Make targeted modifications without rewriting entire files

### Operations

#### replace
- **searchText** (required): Text to find and replace
- **replaceText** (required): Replacement text
- Supports global replacement of all occurrences

#### insert
- **lineNumber** (required): Line position to insert at (1-based)
- **content** (required): Content to insert

#### delete
- **lineNumber** (required): Line number to delete (1-based)

#### create
- **content** (required): Initial file content

### Examples
```json
{"path": "file.txt", "operation": "replace", "searchText": "old", "replaceText": "new"}
{"path": "file.txt", "operation": "insert", "lineNumber": 5, "content": "new line"}
{"path": "file.txt", "operation": "delete", "lineNumber": 3}
{"path": "new.txt", "operation": "create", "content": "Hello World"}
```

---

## WEB Tool

**Category:** Web  
**Description:** For retrieving and fetching real-time and up-to-date information from the internet

### Primary Use Cases
- Search for current information and news
- Fetch content from specific websites
- Research topics and gather data
- Verify facts and get latest updates
- Access documentation and resources

### Operations

#### search
- **query** (required): Search terms to look for
- **maxResults** (optional): Maximum number of results (default: 5)

#### fetch
- **url** (required): URL to fetch content from

### Examples
```json
{"operation": "search", "query": "TypeScript best practices 2024"}
{"operation": "fetch", "url": "https://api.github.com/repos/microsoft/typescript"}
```

---

## Best Practices

### General Guidelines
1. **Always validate inputs** before executing tools
2. **Handle errors gracefully** with meaningful messages
3. **Use appropriate tools** for specific tasks
4. **Consider security implications** of all operations
5. **Provide clear feedback** to users about tool execution

### Tool Selection
- Use **bash** for system commands and process management
- Use **grep** for content-based searches
- Use **glob** for file pattern matching
- Use **write** for creating or completely replacing files
- Use **edit** for precise file modifications
- Use **web** for internet research and data retrieval

### Error Handling
- All tools include comprehensive error handling
- Retryable errors are automatically retried with exponential backoff
- Non-retryable errors provide clear guidance for resolution
- Security violations are blocked with explanatory messages

### Performance Considerations
- Tools include timeouts to prevent hanging operations
- Output is limited to prevent memory issues
- Recursive operations have depth limits
- Large file operations are optimized for efficiency

---

## Extending the Tool System

The tool system is designed to be easily extensible. To add a new tool:

1. Implement the `Tool` interface
2. Add comprehensive parameter validation
3. Include detailed documentation
4. Register the tool with the `ToolManager`
5. Add appropriate tests

See the existing tool implementations for examples and patterns to follow.
