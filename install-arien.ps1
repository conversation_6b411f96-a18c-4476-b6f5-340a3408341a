# Arien AI CLI - Windows PowerShell Installation Script
# Supports Windows 10/11 with PowerShell 5.1+ or PowerShell Core

param(
    [Parameter(Position=0)]
    [ValidateSet("install", "update", "uninstall", "help")]
    [string]$Action = "install"
)

# Configuration
$PackageName = "arien-ai-cli"
$BinaryName = "arien"
$RepoUrl = "https://github.com/arien-ai/cli.git"
$InstallDir = "$env:USERPROFILE\.arien-ai"
$BinDir = "$env:USERPROFILE\.local\bin"

# Colors for output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Status {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Blue"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[SUCCESS] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARNING] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Write-Header {
    Write-Host ""
    Write-ColorOutput "╔══════════════════════════════════════════════════════════════╗" "Cyan"
    Write-ColorOutput "║                     ARIEN AI CLI INSTALLER                   ║" "Cyan"
    Write-ColorOutput "║              Sophisticated AI-Powered Terminal               ║" "Cyan"
    Write-ColorOutput "╚══════════════════════════════════════════════════════════════╝" "Cyan"
    Write-Host ""
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Test-Prerequisites {
    Write-Status "Checking prerequisites..."
    
    # Check Node.js
    if (-not (Test-Command "node")) {
        Write-Error "Node.js is not installed. Please install Node.js 18+ first."
        Write-Status "Visit: https://nodejs.org/"
        exit 1
    }
    
    # Check Node.js version
    $nodeVersion = node --version
    $versionNumber = [int]($nodeVersion -replace "v(\d+)\..*", '$1')
    if ($versionNumber -lt 18) {
        Write-Error "Node.js version 18+ is required. Current version: $nodeVersion"
        exit 1
    }
    
    # Check npm
    if (-not (Test-Command "npm")) {
        Write-Error "npm is not installed. Please install npm first."
        exit 1
    }
    
    # Check git
    if (-not (Test-Command "git")) {
        Write-Error "git is not installed. Please install git first."
        exit 1
    }
    
    Write-Success "All prerequisites satisfied"
}

function Install-ArienAI {
    Write-Status "Installing Arien AI CLI..."
    
    # Create installation directory
    if (-not (Test-Path $InstallDir)) {
        New-Item -ItemType Directory -Path $InstallDir -Force | Out-Null
    }
    if (-not (Test-Path $BinDir)) {
        New-Item -ItemType Directory -Path $BinDir -Force | Out-Null
    }
    
    # Clone or update repository
    if (Test-Path "$InstallDir\.git") {
        Write-Status "Updating existing installation..."
        Set-Location $InstallDir
        git pull origin main
    }
    else {
        Write-Status "Cloning repository..."
        git clone $RepoUrl $InstallDir
        Set-Location $InstallDir
    }
    
    # Install dependencies
    Write-Status "Installing dependencies..."
    npm install
    
    # Build the project
    Write-Status "Building project..."
    npm run build
    
    # Create batch file for Windows
    $batchContent = @"
@echo off
node "$InstallDir\dist\index.js" %*
"@
    $batchPath = "$BinDir\$BinaryName.bat"
    $batchContent | Out-File -FilePath $batchPath -Encoding ASCII
    
    # Add to PATH if not already there
    Add-ToPath
    
    Write-Success "Arien AI CLI installed successfully!"
    Write-Status "Run 'arien setup' to configure your AI providers"
}

function Add-ToPath {
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    
    if ($currentPath -notlike "*$BinDir*") {
        Write-Status "Adding $BinDir to PATH"
        $newPath = "$BinDir;$currentPath"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Warning "Please restart your terminal or PowerShell session"
    }
}

function Update-ArienAI {
    Write-Status "Updating Arien AI CLI..."
    
    if (-not (Test-Path $InstallDir)) {
        Write-Error "Arien AI CLI is not installed. Run with 'install' option first."
        exit 1
    }
    
    Install-ArienAI
}

function Uninstall-ArienAI {
    Write-Status "Uninstalling Arien AI CLI..."
    
    # Remove installation directory
    if (Test-Path $InstallDir) {
        Remove-Item -Path $InstallDir -Recurse -Force
        Write-Success "Removed installation directory"
    }
    
    # Remove binary
    $batchPath = "$BinDir\$BinaryName.bat"
    if (Test-Path $batchPath) {
        Remove-Item -Path $batchPath -Force
        Write-Success "Removed binary"
    }
    
    # Remove from PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($currentPath -like "*$BinDir*") {
        $newPath = $currentPath -replace [regex]::Escape("$BinDir;"), ""
        $newPath = $newPath -replace [regex]::Escape(";$BinDir"), ""
        $newPath = $newPath -replace [regex]::Escape($BinDir), ""
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
        Write-Success "Removed from PATH"
    }
    
    Write-Success "Arien AI CLI uninstalled successfully!"
}

function Show-Usage {
    Write-Host "Usage: .\install-arien.ps1 [install|update|uninstall|help]"
    Write-Host ""
    Write-Host "Commands:"
    Write-Host "  install    - Install Arien AI CLI"
    Write-Host "  update     - Update existing installation"
    Write-Host "  uninstall  - Remove Arien AI CLI"
    Write-Host "  help       - Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\install-arien.ps1 install"
    Write-Host "  .\install-arien.ps1 update"
    Write-Host "  .\install-arien.ps1 uninstall"
}

# Main execution
Write-Header

switch ($Action) {
    "install" {
        Test-Prerequisites
        Install-ArienAI
    }
    "update" {
        Test-Prerequisites
        Update-ArienAI
    }
    "uninstall" {
        Uninstall-ArienAI
    }
    "help" {
        Show-Usage
    }
    default {
        Write-Error "Unknown command: $Action"
        Show-Usage
        exit 1
    }
}
