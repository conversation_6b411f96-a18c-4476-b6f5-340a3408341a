#!/bin/bash

# Arien AI CLI - Universal Installation Script
# Supports Windows 11 WSL, macOS, and Linux

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PACKAGE_NAME="arien-ai-cli"
BINARY_NAME="arien"
REPO_URL="https://github.com/arien-ai/cli.git"
INSTALL_DIR="$HOME/.arien-ai"
BIN_DIR="$HOME/.local/bin"

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if grep -q Microsoft /proc/version 2>/dev/null; then
            OS="wsl"
        else
            OS="linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]]; then
        OS="windows"
    else
        OS="unknown"
    fi
}

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                     ARIEN AI CLI INSTALLER                   ║"
    echo "║              Sophisticated AI-Powered Terminal               ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 22+ first."
        print_status "Visit: https://nodejs.org/"
        exit 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check npm
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    # Check git
    if ! command_exists git; then
        print_error "git is not installed. Please install git first."
        exit 1
    fi
    
    print_success "All prerequisites satisfied"
}

# Install function
install_arien() {
    print_status "Installing Arien AI CLI..."
    
    # Create installation directory
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$BIN_DIR"
    
    # Clone or update repository
    if [ -d "$INSTALL_DIR/.git" ]; then
        print_status "Updating existing installation..."
        cd "$INSTALL_DIR"
        git pull origin main
    else
        print_status "Cloning repository..."
        git clone "$REPO_URL" "$INSTALL_DIR"
        cd "$INSTALL_DIR"
    fi
    
    # Install dependencies
    print_status "Installing dependencies..."
    npm install
    
    # Build the project
    print_status "Building project..."
    npm run build
    
    # Create symlink or copy binary
    if [ "$OS" = "windows" ] || [ "$OS" = "wsl" ]; then
        # For Windows/WSL, copy the files
        cp "$INSTALL_DIR/dist/index.js" "$BIN_DIR/$BINARY_NAME"
        chmod +x "$BIN_DIR/$BINARY_NAME"
    else
        # For Unix systems, create symlink
        ln -sf "$INSTALL_DIR/dist/index.js" "$BIN_DIR/$BINARY_NAME"
    fi
    
    # Add to PATH if not already there
    add_to_path
    
    print_success "Arien AI CLI installed successfully!"
    print_status "Run 'arien setup' to configure your AI providers"
}

# Add to PATH
add_to_path() {
    local shell_rc=""
    
    # Detect shell and set appropriate RC file
    if [ -n "$ZSH_VERSION" ]; then
        shell_rc="$HOME/.zshrc"
    elif [ -n "$BASH_VERSION" ]; then
        if [ "$OS" = "macos" ]; then
            shell_rc="$HOME/.bash_profile"
        else
            shell_rc="$HOME/.bashrc"
        fi
    else
        shell_rc="$HOME/.profile"
    fi
    
    # Check if BIN_DIR is already in PATH
    if [[ ":$PATH:" != *":$BIN_DIR:"* ]]; then
        print_status "Adding $BIN_DIR to PATH in $shell_rc"
        echo "" >> "$shell_rc"
        echo "# Arien AI CLI" >> "$shell_rc"
        echo "export PATH=\"$BIN_DIR:\$PATH\"" >> "$shell_rc"
        print_warning "Please restart your terminal or run: source $shell_rc"
    fi
}

# Update function
update_arien() {
    print_status "Updating Arien AI CLI..."
    
    if [ ! -d "$INSTALL_DIR" ]; then
        print_error "Arien AI CLI is not installed. Run with 'install' option first."
        exit 1
    fi
    
    install_arien
}

# Uninstall function
uninstall_arien() {
    print_status "Uninstalling Arien AI CLI..."
    
    # Remove installation directory
    if [ -d "$INSTALL_DIR" ]; then
        rm -rf "$INSTALL_DIR"
        print_success "Removed installation directory"
    fi
    
    # Remove binary
    if [ -f "$BIN_DIR/$BINARY_NAME" ]; then
        rm -f "$BIN_DIR/$BINARY_NAME"
        print_success "Removed binary"
    fi
    
    # Remove from PATH (manual step)
    print_warning "Please manually remove the PATH entry from your shell configuration file"
    print_status "Look for '# Arien AI CLI' section in ~/.bashrc, ~/.zshrc, or ~/.profile"
    
    print_success "Arien AI CLI uninstalled successfully!"
}

# Show usage
show_usage() {
    echo "Usage: $0 [install|update|uninstall]"
    echo ""
    echo "Commands:"
    echo "  install    - Install Arien AI CLI"
    echo "  update     - Update existing installation"
    echo "  uninstall  - Remove Arien AI CLI"
    echo ""
    echo "Examples:"
    echo "  $0 install"
    echo "  $0 update"
    echo "  $0 uninstall"
}

# Main function
main() {
    print_header
    
    detect_os
    print_status "Detected OS: $OS"
    
    case "${1:-install}" in
        "install")
            check_prerequisites
            install_arien
            ;;
        "update")
            check_prerequisites
            update_arien
            ;;
        "uninstall")
            uninstall_arien
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
