{"name": "arien-ai-cli", "version": "1.0.0", "description": "Sophisticated AI-powered CLI terminal system with LLM integration and autonomous tool execution", "main": "dist/index.js", "bin": {"arien": "dist/index.js"}, "type": "module", "engines": {"node": ">=22.0.0"}, "scripts": {"build": "tsc", "build:unix": "tsc && chmod +x dist/index.js", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "typecheck": "tsc --noEmit", "install:global": "npm run build && npm install -g .", "uninstall:global": "npm uninstall -g arien-ai-cli"}, "keywords": ["ai", "cli", "terminal", "llm", "deepseek", "ollama", "typescript", "assistant", "function-calling", "autonomous", "streaming"], "author": "Arien AI Team", "license": "MIT", "dependencies": {"axios": "^1.7.9", "boxen": "^8.0.1", "chalk": "^5.4.1", "cheerio": "^1.0.0", "cli-cursor": "^5.0.0", "cli-spinners": "^3.2.0", "commander": "^12.1.0", "conf": "^13.0.1", "duckduckgo-search": "^1.0.6", "figlet": "^1.8.0", "glob": "^11.0.0", "inquirer": "^12.1.0", "keypress": "^0.2.1", "node-fetch": "^3.3.2", "ora": "^8.1.1", "strip-ansi": "^7.1.0", "ws": "^8.18.0"}, "devDependencies": {"@types/figlet": "^1.7.0", "@types/inquirer": "^9.0.8", "@types/node": "^22.10.2", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "@vitest/coverage-v8": "^2.1.8", "eslint": "^9.18.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "prettier": "^3.4.2", "rimraf": "^6.0.1", "tsx": "^4.19.2", "typescript": "^5.8.3", "vitest": "^2.1.8"}}