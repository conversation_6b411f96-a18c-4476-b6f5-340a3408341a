import readline from 'readline';
import chalk from 'chalk';
import figlet from 'figlet';
import boxen from 'boxen';
import { EventEmitter } from 'events';
import { UIState, SlashCommand } from '../types/index.js';
import { createSpinner } from '../utils/animations.js';

export class CLIInterface extends EventEmitter {
  private rl: readline.Interface;
  private state: UIState;
  private currentSpinner: ReturnType<typeof createSpinner> | null = null;

  constructor() {
    super();
    this.state = {
      isAuthenticated: false,
      currentProvider: '',
      currentModel: '',
      isStreaming: false,
      showCommandPalette: false,
      commandPaletteQuery: '',
      selectedCommandIndex: 0,
      availableCommands: this.getDefaultCommands(),
    };

    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: this.getPrompt(),
    });

    this.setupEventHandlers();
  }

  async initialize(): Promise<void> {
    this.showWelcomeBanner();
    this.setupKeyHandlers();
  }

  private showWelcomeBanner(): void {
    console.clear();
    
    const title = figlet.textSync('ARIEN AI', {
      font: 'ANSI Shadow',
      horizontalLayout: 'default',
      verticalLayout: 'default',
    });

    const banner = boxen(
      chalk.cyan(title) + '\n\n' +
      chalk.white('Sophisticated AI-powered CLI terminal system') + '\n' +
      chalk.gray('Type /help for commands or start chatting!'),
      {
        padding: 1,
        margin: 1,
        borderStyle: 'round',
        borderColor: 'cyan',
        backgroundColor: 'black',
      }
    );

    console.log(banner);
  }

  private setupEventHandlers(): void {
    this.rl.on('line', (input) => {
      this.handleInput(input.trim());
    });

    this.rl.on('SIGINT', () => {
      if (this.state.isStreaming) {
        this.stopStreaming();
      } else {
        this.handleExit();
      }
    });

    // Handle double ESC for interruption
    let escapeCount = 0;
    process.stdin.on('keypress', (_str, key) => {
      if (key && key.name === 'escape') {
        escapeCount++;
        setTimeout(() => { escapeCount = 0; }, 500);

        if (escapeCount >= 2 && this.state.isStreaming) {
          this.interruptStreaming();
          escapeCount = 0;
        }
      }
    });
  }

  private setupKeyHandlers(): void {
    // Enable keypress events
    if (process.stdin.isTTY) {
      process.stdin.setRawMode(true);
    }
    
    require('keypress')(process.stdin);
    
    process.stdin.on('keypress', (ch, key) => {
      if (this.state.showCommandPalette) {
        this.handleCommandPaletteKey(ch, key);
      }
    });
  }

  private handleInput(input: string): void {
    if (input.startsWith('/')) {
      this.handleSlashCommand(input);
    } else if (input.trim()) {
      this.emit('userMessage', input);
    }
    
    if (!this.state.showCommandPalette) {
      this.rl.prompt();
    }
  }

  private handleSlashCommand(input: string): void {
    const parts = input.slice(1).split(' ');
    const command = parts[0];
    const args = parts.slice(1);

    if (command === '') {
      this.showCommandPalette();
      return;
    }

    const slashCommand = this.state.availableCommands.find((cmd: SlashCommand) => cmd.name === command);
    if (slashCommand) {
      slashCommand.handler(args);
    } else {
      this.displayError(`Unknown command: /${command}`);
      this.showAvailableCommands();
    }
  }

  private showCommandPalette(): void {
    this.state.showCommandPalette = true;
    this.state.selectedCommandIndex = 0;
    this.renderCommandPalette();
  }

  private hideCommandPalette(): void {
    this.state.showCommandPalette = false;
    this.clearCommandPalette();
    this.rl.prompt();
  }

  private renderCommandPalette(): void {
    const commands = this.state.availableCommands;
    const maxDisplay = 8;
    
    console.log('\n' + chalk.cyan('Available Commands:'));
    console.log(chalk.gray('Use ↑↓ to navigate, Enter to select, Esc to cancel\n'));

    commands.slice(0, maxDisplay).forEach((cmd: SlashCommand, index: number) => {
      const isSelected = index === this.state.selectedCommandIndex;
      const prefix = isSelected ? chalk.cyan('► ') : '  ';
      const cmdText = isSelected ? chalk.cyan.bold(`/${cmd.name}`) : chalk.white(`/${cmd.name}`);
      const desc = isSelected ? chalk.cyan(cmd.description) : chalk.gray(cmd.description);

      console.log(`${prefix}${cmdText} - ${desc}`);
    });

    if (commands.length > maxDisplay) {
      console.log(chalk.gray(`  ... and ${commands.length - maxDisplay} more`));
    }
  }

  private clearCommandPalette(): void {
    // Move cursor up and clear lines
    const linesToClear = Math.min(this.state.availableCommands.length + 3, 12);
    for (let i = 0; i < linesToClear; i++) {
      process.stdout.write('\x1b[1A\x1b[2K');
    }
  }

  private handleCommandPaletteKey(_ch: string, key: any): void {
    if (!key) return;

    switch (key.name) {
      case 'up':
        this.state.selectedCommandIndex = Math.max(0, this.state.selectedCommandIndex - 1);
        this.clearCommandPalette();
        this.renderCommandPalette();
        break;

      case 'down':
        this.state.selectedCommandIndex = Math.min(
          this.state.availableCommands.length - 1,
          this.state.selectedCommandIndex + 1
        );
        this.clearCommandPalette();
        this.renderCommandPalette();
        break;

      case 'return':
        const selectedCommand = this.state.availableCommands[this.state.selectedCommandIndex];
        this.hideCommandPalette();
        if (selectedCommand) {
          selectedCommand.handler([]);
        }
        break;

      case 'escape':
        this.hideCommandPalette();
        break;
    }
  }

  private getDefaultCommands(): SlashCommand[] {
    return [
      {
        name: 'help',
        description: 'Show available commands and usage information',
        usage: '/help [command]',
        handler: async (args: string[]) => { this.showHelp(args[0]); },
      },
      {
        name: 'model',
        description: 'Switch between available models',
        usage: '/model [model-name]',
        handler: async (args: string[]) => { this.emit('switchModel', args[0]); },
      },
      {
        name: 'provider',
        description: 'Change AI provider',
        usage: '/provider [provider-name]',
        handler: async (args: string[]) => { this.emit('switchProvider', args[0]); },
      },
      {
        name: 'config',
        description: 'Show or modify configuration',
        usage: '/config [key] [value]',
        handler: async (args: string[]) => { this.emit('configCommand', args); },
      },
      {
        name: 'clear',
        description: 'Clear the terminal screen',
        usage: '/clear',
        handler: async () => { this.clearScreen(); },
      },
      {
        name: 'history',
        description: 'Show conversation history',
        usage: '/history',
        handler: async () => { this.emit('showHistory'); },
      },
      {
        name: 'tools',
        description: 'List available tools and their documentation',
        usage: '/tools [tool-name]',
        handler: async (args: string[]) => { this.emit('showTools', args[0]); },
      },
      {
        name: 'exit',
        description: 'Exit the application',
        usage: '/exit',
        handler: async () => { this.handleExit(); },
      },
    ];
  }

  // Public methods for external control
  updateState(updates: Partial<UIState>): void {
    this.state = { ...this.state, ...updates };
    this.rl.setPrompt(this.getPrompt());
  }

  displayMessage(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {
    const colors = {
      info: chalk.blue,
      success: chalk.green,
      warning: chalk.yellow,
      error: chalk.red,
    };

    console.log(colors[type](`${message}`));
    this.rl.prompt();
  }

  displayError(message: string): void {
    this.displayMessage(`Error: ${message}`, 'error');
  }

  displayAIResponse(content: string, isStreaming = false): void {
    if (isStreaming) {
      process.stdout.write(chalk.white(content));
    } else {
      console.log(chalk.white(content));
      this.rl.prompt();
    }
  }

  showStreamingProgress(): void {
    if (!this.state.isStreaming) return;

    const spinner = createSpinner('AI is thinking', 'ball');
    spinner.start();

    // Auto-hide after a reasonable time or when streaming stops
    const checkInterval = setInterval(() => {
      if (!this.state.isStreaming) {
        spinner.stop();
        clearInterval(checkInterval);
      }
    }, 100);
  }

  startStreaming(): void {
    this.state.isStreaming = true;
    console.log(chalk.gray('\n[AI is responding... Press ESC twice to interrupt]\n'));
    // Note: readline interface doesn't have a paused property, we'll track state manually
  }

  stopStreaming(): void {
    this.state.isStreaming = false;
    console.log(chalk.gray('\n[Response complete]'));
    this.rl.prompt();
  }

  interruptStreaming(): void {
    this.state.isStreaming = false;
    console.log(chalk.red('\n[Response interrupted by user]'));
    this.emit('streamInterrupted');
    this.rl.prompt();
  }

  showSpinner(text: string): void {
    this.currentSpinner = createSpinner(text);
    this.currentSpinner.start();
  }

  hideSpinner(): void {
    if (this.currentSpinner) {
      this.currentSpinner.stop();
      this.currentSpinner = null;
    }
  }

  private getPrompt(): string {
    const provider = this.state.currentProvider ? chalk.cyan(`[${this.state.currentProvider}]`) : '';
    const model = this.state.currentModel ? chalk.gray(`(${this.state.currentModel})`) : '';
    return `${provider}${model} ${chalk.green('>')} `;
  }

  private showHelp(commandName?: string): void {
    if (commandName) {
      const command = this.state.availableCommands.find((cmd: SlashCommand) => cmd.name === commandName);
      if (command) {
        console.log(chalk.cyan(`\n/${command.name}`));
        console.log(chalk.white(command.description));
        console.log(chalk.gray(`Usage: ${command.usage}\n`));
      } else {
        this.displayError(`Command not found: ${commandName}`);
      }
    } else {
      this.showAvailableCommands();
    }
  }

  private showAvailableCommands(): void {
    console.log(chalk.cyan('\nAvailable Commands:'));
    this.state.availableCommands.forEach((cmd: SlashCommand) => {
      console.log(`  ${chalk.white(`/${cmd.name}`)} - ${chalk.gray(cmd.description)}`);
    });
    console.log(chalk.gray('\nType /help [command] for detailed usage information\n'));
  }

  private clearScreen(): void {
    console.clear();
    this.showWelcomeBanner();
  }

  private handleExit(): void {
    console.log(chalk.cyan('\nGoodbye! 👋'));
    process.exit(0);
  }

  close(): void {
    this.rl.close();
    if (this.currentSpinner) {
      this.currentSpinner.stop();
    }
  }
}
