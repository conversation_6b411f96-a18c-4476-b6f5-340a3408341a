import axios, { AxiosInstance } from 'axios';
import {
  AIProvider,
  ProviderConfig,
  ChatMessage,
  ChatOptions,
  ChatResponse,
  ChatChunk,
  FunctionCall,
  FunctionResult,
  FunctionDefinition,
} from '../../types/index.js';
import { createError, ErrorCodes } from '../../utils/errors.js';
import { withNetworkRetry } from '../../utils/retry.js';

export class DeepSeekProvider implements AIProvider {
  public readonly name = 'deepseek';
  public readonly type = 'api' as const;
  public readonly models = ['deepseek-chat', 'deepseek-reasoner'];

  private client: AxiosInstance;
  private config: ProviderConfig;

  constructor() {
    this.client = axios.create({
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    this.config = {} as ProviderConfig;
  }

  async authenticate(config: ProviderConfig): Promise<boolean> {
    try {
      this.config = config;
      const baseUrl = config.baseUrl || 'https://api.deepseek.com/v1';

      this.client.defaults.baseURL = baseUrl;
      this.client.defaults.headers.Authorization = `Bearer ${config.apiKey}`;

      // Test authentication with a simple request
      await withNetworkRetry(async () => {
        const response = await this.client.get('/models');
        return response.data;
      });

      return true;
    } catch (error) {
      const axiosError = error as any;
      if (axiosError.response?.status === 401) {
        throw createError(
          'Invalid API key for DeepSeek',
          'AUTH_INVALID_API_KEY',
          'auth',
          false,
          { provider: 'deepseek' }
        );
      }

      throw createError(
        `DeepSeek authentication failed: ${(error as Error).message}`,
        'AUTH_PROVIDER_UNAVAILABLE',
        'auth',
        true,
        { provider: 'deepseek' }
      );
    }
  }

  async chat(messages: ChatMessage[], options: ChatOptions = {}): Promise<ChatResponse> {
    try {
      const requestBody = this.buildChatRequest(messages, options);

      const response = await withNetworkRetry(async () => {
        return await this.client.post('/chat/completions', requestBody);
      });

      return this.parseChatResponse(response.data);
    } catch (error) {
      throw this.handleAPIError(error as Error);
    }
  }

  async *streamChat(
    messages: ChatMessage[],
    options: ChatOptions = {}
  ): AsyncGenerator<ChatChunk> {
    try {
      const requestBody = this.buildChatRequest(messages, { ...options, stream: true });

      const response = await withNetworkRetry(async () => {
        return await this.client.post('/chat/completions', requestBody, {
          responseType: 'stream',
        });
      });

      let buffer = '';
      const stream = response.data;

      for await (const chunk of stream) {
        buffer += chunk.toString();
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              yield { done: true };
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const chunk = this.parseStreamChunk(parsed);
              if (chunk) {
                yield chunk;
              }
            } catch (parseError) {
              // Skip invalid JSON chunks
              continue;
            }
          }
        }
      }
    } catch (error) {
      throw this.handleAPIError(error as Error);
    }
  }

  async callFunction(functionCall: FunctionCall): Promise<FunctionResult> {
    // DeepSeek doesn't directly execute functions, but we can format the call
    // for the tool system to handle
    try {
      const args = typeof functionCall.arguments === 'string' 
        ? JSON.parse(functionCall.arguments)
        : functionCall.arguments;

      return {
        success: true,
        result: {
          name: functionCall.name,
          arguments: args,
        },
        output: `Function call prepared: ${functionCall.name}`,
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to parse function arguments: ${(error as Error).message}`,
      };
    }
  }

  private buildChatRequest(messages: ChatMessage[], options: ChatOptions): any {
    const request: any = {
      model: this.config.model,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        ...(msg.name && { name: msg.name }),
      })),
      temperature: options.temperature ?? this.config.temperature ?? 0.7,
      max_tokens: options.maxTokens ?? this.config.maxTokens ?? 4000,
      stream: options.stream ?? false,
    };

    // Add function calling if supported
    if (options.functions && options.functions.length > 0) {
      request.functions = options.functions.map(this.formatFunction);
      if (options.functionCall) {
        request.function_call = options.functionCall;
      }
    }

    return request;
  }

  private formatFunction(func: FunctionDefinition): any {
    return {
      name: func.name,
      description: func.description,
      parameters: func.parameters,
    };
  }

  private parseChatResponse(data: any): ChatResponse {
    const choice = data.choices?.[0];
    if (!choice) {
      throw createError(
        'Invalid response format from DeepSeek',
        'AI_RESPONSE_INVALID',
        'ai',
        false
      );
    }

    const response: ChatResponse = {
      content: choice.message?.content || '',
      usage: data.usage ? {
        promptTokens: data.usage.prompt_tokens,
        completionTokens: data.usage.completion_tokens,
        totalTokens: data.usage.total_tokens,
      } : undefined,
    };

    // Handle function calls
    if (choice.message?.function_call) {
      response.functionCall = {
        name: choice.message.function_call.name,
        arguments: choice.message.function_call.arguments,
      };
    }

    return response;
  }

  private parseStreamChunk(data: any): ChatChunk | null {
    const choice = data.choices?.[0];
    if (!choice) return null;

    const chunk: ChatChunk = {
      done: choice.finish_reason !== null,
    };

    if (choice.delta?.content) {
      chunk.content = choice.delta.content;
    }

    if (choice.delta?.function_call) {
      chunk.functionCall = {
        name: choice.delta.function_call.name,
        arguments: choice.delta.function_call.arguments,
      };
    }

    return chunk;
  }

  private handleAPIError(error: Error): Error {
    const axiosError = error as any;

    if (axiosError.response) {
      const status = axiosError.response.status;
      const message = axiosError.response.data?.error?.message || axiosError.message;

      switch (status) {
        case 401:
          return createError(
            'Invalid API key',
            'AUTH_INVALID_API_KEY',
            'auth',
            false
          );
        case 429:
          return createError(
            'Rate limit exceeded',
            'NETWORK_RATE_LIMITED',
            'network',
            true
          );
        case 503:
          return createError(
            'DeepSeek service unavailable',
            'AI_MODEL_UNAVAILABLE',
            'ai',
            true
          );
        default:
          return createError(
            `DeepSeek API error: ${message}`,
            'AI_RESPONSE_INVALID',
            'ai',
            status >= 500
          );
      }
    }

    if (axiosError.code === 'ECONNABORTED') {
      return createError(
        'Request timeout',
        'NETWORK_TIMEOUT',
        'network',
        true
      );
    }

    return createError(
      `Network error: ${error.message}`,
      'NETWORK_CONNECTION_FAILED',
      'network',
      true
    );
  }
}
