import axios, { AxiosInstance } from 'axios';
import {
  AIProvider,
  ProviderConfig,
  ChatMessage,
  ChatOptions,
  ChatResponse,
  ChatChunk,
  FunctionCall,
  FunctionResult,
} from '../../types/index.js';
import { createError, ErrorCodes } from '../../utils/errors.js';
import { withNetworkRetry } from '../../utils/retry.js';

export class OllamaProvider implements AIProvider {
  public readonly name = 'ollama';
  public readonly type = 'local' as const;
  public models: string[] = [];

  private client: AxiosInstance;
  private config: ProviderConfig;

  constructor() {
    this.client = axios.create({
      timeout: 60000, // Longer timeout for local models
      headers: {
        'Content-Type': 'application/json',
      },
    });
    this.config = {} as ProviderConfig;
  }

  async authenticate(config: ProviderConfig): Promise<boolean> {
    try {
      this.config = config;
      const baseUrl = config.baseUrl || 'http://localhost:11434';

      this.client.defaults.baseURL = baseUrl;

      // Test connection and get available models
      const modelsResponse = await withNetworkRetry(async () => {
        return await this.client.get('/api/tags');
      });

      this.models = modelsResponse.data.models?.map((model: any) => model.name) || [];

      if (this.models.length === 0) {
        throw createError(
          'No models available in Ollama',
          'AI_MODEL_UNAVAILABLE',
          'ai',
          false
        );
      }

      return true;
    } catch (error) {
      const axiosError = error as any;
      
      if (axiosError.code === 'ECONNREFUSED') {
        throw createError(
          'Ollama server is not running. Please start Ollama first.',
          'AUTH_PROVIDER_UNAVAILABLE',
          'auth',
          false,
          { provider: 'ollama', baseUrl: this.config.baseUrl }
        );
      }

      throw createError(
        `Ollama connection failed: ${(error as Error).message}`,
        'AUTH_PROVIDER_UNAVAILABLE',
        'auth',
        true,
        { provider: 'ollama' }
      );
    }
  }

  async chat(messages: ChatMessage[], options: ChatOptions = {}): Promise<ChatResponse> {
    try {
      const requestBody = this.buildChatRequest(messages, options);

      const response = await withNetworkRetry(async () => {
        return await this.client.post('/api/chat', requestBody);
      });

      return this.parseChatResponse(response.data);
    } catch (error) {
      throw this.handleAPIError(error as Error);
    }
  }

  async *streamChat(
    messages: ChatMessage[],
    options: ChatOptions = {}
  ): AsyncGenerator<ChatChunk> {
    try {
      const requestBody = this.buildChatRequest(messages, { ...options, stream: true });

      const response = await withNetworkRetry(async () => {
        return await this.client.post('/api/chat', requestBody, {
          responseType: 'stream',
        });
      });

      let buffer = '';
      const stream = response.data;

      for await (const chunk of stream) {
        buffer += chunk.toString();
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const parsed = JSON.parse(line);
              const chunk = this.parseStreamChunk(parsed);
              if (chunk) {
                yield chunk;
              }
            } catch (parseError) {
              // Skip invalid JSON chunks
              continue;
            }
          }
        }
      }
    } catch (error) {
      throw this.handleAPIError(error as Error);
    }
  }

  async callFunction(functionCall: FunctionCall): Promise<FunctionResult> {
    // Ollama doesn't have native function calling, but we can simulate it
    try {
      const args = typeof functionCall.arguments === 'string' 
        ? JSON.parse(functionCall.arguments)
        : functionCall.arguments;

      // Use the model to understand and format the function call
      const prompt = `Execute the following function call:
Function: ${functionCall.name}
Arguments: ${JSON.stringify(args, null, 2)}

Please provide a structured response indicating how this function should be executed.`;

      const response = await this.chat([
        { role: 'user', content: prompt }
      ]);

      return {
        success: true,
        result: {
          name: functionCall.name,
          arguments: args,
          modelResponse: response.content,
        },
        output: `Function call interpreted by model: ${functionCall.name}`,
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to process function call: ${(error as Error).message}`,
      };
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/api/tags');
      this.models = response.data.models?.map((model: any) => model.name) || [];
      return this.models;
    } catch (error) {
      throw this.handleAPIError(error as Error);
    }
  }

  async pullModel(modelName: string): Promise<void> {
    try {
      await this.client.post('/api/pull', { name: modelName });
      // Refresh available models
      await this.getAvailableModels();
    } catch (error) {
      throw this.handleAPIError(error as Error);
    }
  }

  private buildChatRequest(messages: ChatMessage[], options: ChatOptions): any {
    const ollamaMessages = messages.map(msg => ({
      role: msg.role === 'function' ? 'assistant' : msg.role,
      content: msg.content,
    }));

    const request: any = {
      model: this.config.model,
      messages: ollamaMessages,
      stream: options.stream ?? false,
      options: {
        temperature: options.temperature ?? this.config.temperature ?? 0.7,
        num_predict: options.maxTokens ?? this.config.maxTokens ?? 4000,
      },
    };

    return request;
  }

  private parseChatResponse(data: any): ChatResponse {
    if (!data.message) {
      throw createError(
        'Invalid response format from Ollama',
        'AI_RESPONSE_INVALID',
        'ai',
        false
      );
    }

    return {
      content: data.message.content || '',
      usage: data.prompt_eval_count ? {
        promptTokens: data.prompt_eval_count,
        completionTokens: data.eval_count,
        totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0),
      } : undefined,
    };
  }

  private parseStreamChunk(data: any): ChatChunk | null {
    if (!data.message) return null;

    return {
      content: data.message.content || '',
      done: data.done || false,
    };
  }

  private handleAPIError(error: Error): Error {
    const axiosError = error as any;

    if (axiosError.response) {
      const status = axiosError.response.status;
      const message = axiosError.response.data?.error || axiosError.message;

      switch (status) {
        case 404:
          return createError(
            `Model not found: ${this.config.model}`,
            'AI_MODEL_UNAVAILABLE',
            'ai',
            false
          );
        case 503:
          return createError(
            'Ollama service unavailable',
            'AI_MODEL_UNAVAILABLE',
            'ai',
            true
          );
        default:
          return createError(
            `Ollama API error: ${message}`,
            'AI_RESPONSE_INVALID',
            'ai',
            status >= 500
          );
      }
    }

    if (axiosError.code === 'ECONNREFUSED') {
      return createError(
        'Cannot connect to Ollama. Please ensure Ollama is running.',
        'NETWORK_CONNECTION_FAILED',
        'network',
        false
      );
    }

    if (axiosError.code === 'ECONNABORTED') {
      return createError(
        'Request timeout - model may be loading',
        'NETWORK_TIMEOUT',
        'network',
        true
      );
    }

    return createError(
      `Network error: ${error.message}`,
      'NETWORK_CONNECTION_FAILED',
      'network',
      true
    );
  }
}
