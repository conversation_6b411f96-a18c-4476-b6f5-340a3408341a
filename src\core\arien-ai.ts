import inquirer from 'inquirer';
import chalk from 'chalk';
import { CLIInterface } from '../components/cli-interface.js';
import { ConfigManager } from './config/manager.js';
import { ToolManager } from './tools/index.js';
import { SystemPromptManager } from './prompts/system-prompt.js';
import { DeepSeekProvider } from './ai/deepseek.js';
import { OllamaProvider } from './ai/ollama.js';
import {
  AIProvider,
  CommandContext,
  ChatMessage,
  ProviderConfig,
  FunctionCall,
  FunctionDefinition
} from '../types/index.js';
import { withAIRetry } from '../utils/retry.js';
import { createError, ErrorCodes } from '../utils/errors.js';

export class ArienAI {
  private cli: CLIInterface;
  private configManager: ConfigManager;
  private toolManager: ToolManager;
  private systemPromptManager: SystemPromptManager;
  private providers: Map<string, AIProvider> = new Map();
  private currentProvider: AIProvider | null = null;
  private context: CommandContext;
  private conversationHistory: ChatMessage[] = [];

  constructor() {
    this.cli = new CLIInterface();
    this.configManager = new ConfigManager();
    this.toolManager = new ToolManager();
    this.systemPromptManager = new SystemPromptManager(this.toolManager);
    
    this.context = {
      config: this.configManager.getCLIConfig(),
      provider: null as any,
      history: this.conversationHistory,
      isStreaming: false,
    };

    this.initializeProviders();
    this.setupEventHandlers();
  }

  async start(options: any = {}): Promise<void> {
    try {
      await this.cli.initialize();
      
      // Check if configuration exists
      const validation = this.configManager.validateConfig();
      if (!validation.isValid) {
        this.cli.displayMessage('Configuration incomplete. Starting setup wizard...', 'warning');
        await this.runSetupWizard();
      } else {
        await this.initializeCurrentProvider();
      }

      this.cli.displayMessage('Arien AI is ready! Type your message or use /help for commands.', 'success');
      this.startInteractiveSession();
      
    } catch (error) {
      this.cli.displayError(`Failed to start: ${(error as Error).message}`);
      throw error;
    }
  }

  async runSetupWizard(): Promise<void> {
    this.cli.displayMessage('Welcome to Arien AI Setup Wizard!', 'info');
    
    // Provider selection
    const providerChoices = [
      { name: 'DeepSeek (API)', value: 'deepseek' },
      { name: 'Ollama (Local)', value: 'ollama' },
    ];

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Select an AI provider:',
        choices: providerChoices,
      },
    ]);

    await this.setupProvider(provider);
  }

  async setupProvider(providerName: string): Promise<void> {
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw createError(`Provider not found: ${providerName}`, 'CONFIG_INVALID', 'config');
    }

    let config: ProviderConfig;

    if (providerName === 'deepseek') {
      config = await this.setupDeepSeekProvider();
    } else if (providerName === 'ollama') {
      config = await this.setupOllamaProvider();
    } else {
      throw createError(`Unsupported provider: ${providerName}`, 'CONFIG_INVALID', 'config');
    }

    // Test the configuration
    this.cli.showSpinner('Testing provider connection...');
    try {
      const success = await provider.authenticate(config);
      this.cli.hideSpinner();
      
      if (success) {
        await this.configManager.saveProvider(providerName, config);
        this.configManager.setCurrentProvider(providerName);
        this.cli.displayMessage(`Successfully configured ${providerName}!`, 'success');
        
        await this.initializeCurrentProvider();
      } else {
        throw new Error('Authentication failed');
      }
    } catch (error) {
      this.cli.hideSpinner();
      throw createError(
        `Provider setup failed: ${(error as Error).message}`,
        'AUTH_PROVIDER_UNAVAILABLE',
        'auth'
      );
    }
  }

  private async setupDeepSeekProvider(): Promise<ProviderConfig> {
    const questions = [
      {
        type: 'password',
        name: 'apiKey',
        message: 'Enter your DeepSeek API key:',
        validate: (input: string) => input.length > 0 || 'API key is required',
      },
      {
        type: 'input',
        name: 'baseUrl',
        message: 'Enter base URL (press Enter for default):',
        default: 'https://api.deepseek.com/v1',
      },
      {
        type: 'list',
        name: 'model',
        message: 'Select a model:',
        choices: ['deepseek-chat', 'deepseek-reasoner'],
        default: 'deepseek-chat',
      },
    ];

    const answers = await inquirer.prompt(questions as any);
    
    return {
      apiKey: answers.apiKey,
      baseUrl: answers.baseUrl,
      model: answers.model,
      temperature: 0.7,
      maxTokens: 4000,
    };
  }

  private async setupOllamaProvider(): Promise<ProviderConfig> {
    const questions = [
      {
        type: 'input',
        name: 'baseUrl',
        message: 'Enter Ollama server URL:',
        default: 'http://localhost:11434',
      },
    ];

    const answers = await inquirer.prompt(questions as any);
    
    // Try to get available models
    const provider = this.providers.get('ollama') as OllamaProvider;
    const tempConfig = { baseUrl: answers.baseUrl, model: '' };
    
    try {
      await provider.authenticate(tempConfig);
      const models = await provider.getAvailableModels();
      
      if (models.length === 0) {
        this.cli.displayMessage('No models found. You may need to pull a model first.', 'warning');
        const { modelName } = await inquirer.prompt([
          {
            type: 'input',
            name: 'modelName',
            message: 'Enter model name to pull (e.g., llama2, codellama):',
            validate: (input: string) => input.length > 0 || 'Model name is required',
          },
        ]);
        
        this.cli.showSpinner(`Pulling model ${modelName}...`);
        await provider.pullModel(modelName);
        this.cli.hideSpinner();
        
        return {
          baseUrl: answers.baseUrl,
          model: modelName,
          temperature: 0.7,
          maxTokens: 4000,
        };
      } else {
        const { model } = await inquirer.prompt([
          {
            type: 'list',
            name: 'model',
            message: 'Select a model:',
            choices: models,
          },
        ]);
        
        return {
          baseUrl: answers.baseUrl,
          model,
          temperature: 0.7,
          maxTokens: 4000,
        };
      }
    } catch (error) {
      throw createError(
        `Failed to connect to Ollama: ${(error as Error).message}`,
        'AUTH_PROVIDER_UNAVAILABLE',
        'auth'
      );
    }
  }

  private initializeProviders(): void {
    this.providers.set('deepseek', new DeepSeekProvider());
    this.providers.set('ollama', new OllamaProvider());
  }

  private async initializeCurrentProvider(): Promise<void> {
    const currentProviderName = this.configManager.getCurrentProvider();
    if (!currentProviderName) {
      throw createError('No provider configured', 'CONFIG_MISSING_REQUIRED', 'config');
    }

    const provider = this.providers.get(currentProviderName);
    if (!provider) {
      throw createError(`Provider not found: ${currentProviderName}`, 'CONFIG_INVALID', 'config');
    }

    const config = this.configManager.getCurrentProviderConfig();
    if (!config) {
      throw createError('Provider configuration not found', 'CONFIG_INVALID', 'config');
    }

    await provider.authenticate(config);
    this.currentProvider = provider;
    this.context.provider = provider;
    this.context.config = this.configManager.getCLIConfig();

    this.cli.updateState({
      isAuthenticated: true,
      currentProvider: currentProviderName,
      currentModel: config.model,
    });
  }

  private setupEventHandlers(): void {
    this.cli.on('userMessage', (message: string) => {
      this.handleUserMessage(message);
    });

    this.cli.on('switchModel', (modelName?: string) => {
      this.handleSwitchModel(modelName);
    });

    this.cli.on('switchProvider', (providerName?: string) => {
      this.handleSwitchProvider(providerName);
    });

    this.cli.on('configCommand', (args: string[]) => {
      this.handleConfigCommand(args);
    });

    this.cli.on('showHistory', () => {
      this.showConversationHistory();
    });

    this.cli.on('showTools', (toolName?: string) => {
      this.handleShowTools(toolName);
    });

    this.cli.on('streamInterrupted', () => {
      this.handleStreamInterruption();
    });
  }

  private async handleUserMessage(message: string): Promise<void> {
    if (!this.currentProvider) {
      this.cli.displayError('No AI provider configured. Run setup first.');
      return;
    }

    try {
      // Add user message to history
      const userMessage: ChatMessage = { role: 'user', content: message };
      this.conversationHistory.push(userMessage);

      // Prepare messages with enhanced system prompt
      const systemPrompt = this.systemPromptManager.generateSystemPrompt();
      const messages: ChatMessage[] = [
        { role: 'system', content: systemPrompt },
        ...this.conversationHistory,
      ];

      // Get function definitions for the AI
      const functions = this.toolManager.getFunctionDefinitions();

      this.cli.startStreaming();

      let responseContent = '';
      let functionCalls: FunctionCall[] = [];

      // Use retry logic for AI responses
      const response = await withAIRetry(async () => {
        // Stream the response
        const streamGenerator = this.currentProvider!.streamChat(messages, {
          functions,
          functionCall: 'auto',
          temperature: this.context.config.temperature,
          maxTokens: this.context.config.maxTokens,
        });

        for await (const chunk of streamGenerator) {
          if (chunk.content) {
            responseContent += chunk.content;
            this.cli.displayAIResponse(chunk.content, true);
          }

          if (chunk.functionCall) {
            functionCalls.push(chunk.functionCall as FunctionCall);
          }

          if (chunk.done) {
            break;
          }
        }

        return { content: responseContent, functionCalls };
      });

      this.cli.stopStreaming();

      // Handle function calls if any
      if (response.functionCalls.length > 0) {
        await this.executeFunctionCalls(response.functionCalls);
      }

      // Add assistant response to history
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: response.content
      };
      this.conversationHistory.push(assistantMessage);

      // Record performance metrics for self-learning
      this.systemPromptManager.recordPromptPerformance(
        'user_interaction',
        true,
        Date.now(),
        undefined
      );

    } catch (error) {
      this.cli.stopStreaming();

      // Implement "never give up" logic
      await this.handleErrorWithRetry(error as Error, message);
    }
  }

  private async executeFunctionCalls(functionCalls: FunctionCall[]): Promise<void> {
    for (const functionCall of functionCalls) {
      this.cli.displayMessage(`Executing tool: ${functionCall.name}`, 'info');
      
      try {
        const args = typeof functionCall.arguments === 'string' 
          ? JSON.parse(functionCall.arguments)
          : functionCall.arguments;

        const result = await this.toolManager.executeTool(functionCall.name, args);
        
        if (result.success) {
          this.cli.displayMessage(`Tool completed: ${result.output || 'Success'}`, 'success');
        } else {
          this.cli.displayError(`Tool failed: ${result.error}`);
        }

        // Add function result to conversation history
        const functionMessage: ChatMessage = {
          role: 'function',
          name: functionCall.name,
          content: JSON.stringify(result),
        };
        this.conversationHistory.push(functionMessage);

      } catch (error) {
        this.cli.displayError(`Function execution failed: ${(error as Error).message}`);
      }
    }
  }

  private startInteractiveSession(): void {
    // The CLI interface handles the interactive loop
    // This method can be used for any additional setup
  }

  // Additional methods for CLI commands
  async testProvider(providerName: string): Promise<void> {
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw createError(`Provider not found: ${providerName}`, 'CONFIG_INVALID', 'config');
    }

    const config = this.configManager.getProvider(providerName);
    if (!config) {
      throw createError(`No configuration found for ${providerName}`, 'CONFIG_INVALID', 'config');
    }

    this.cli.showSpinner(`Testing ${providerName}...`);
    try {
      await provider.authenticate(config);
      this.cli.hideSpinner();
      this.cli.displayMessage(`${providerName} connection successful!`, 'success');
    } catch (error) {
      this.cli.hideSpinner();
      throw error;
    }
  }

  async listTools(): Promise<void> {
    const tools = this.toolManager.getAllTools();
    
    console.log(chalk.cyan('\nAvailable Tools:'));
    tools.forEach(tool => {
      console.log(`  ${chalk.white(tool.name)} (${chalk.gray(tool.category)}) - ${tool.description}`);
    });
    console.log(chalk.gray('\nUse /tools --docs <tool-name> for detailed documentation\n'));
  }

  async showToolDocs(toolName: string): Promise<void> {
    const documentation = this.toolManager.getToolDocumentation(toolName);
    console.log(documentation);
  }

  async showAllToolDocs(): Promise<void> {
    const documentation = this.toolManager.getAllToolsDocumentation();
    console.log(documentation);
  }

  private async handleSwitchModel(modelName?: string): Promise<void> {
    if (!this.currentProvider) {
      this.cli.displayError('No provider configured');
      return;
    }

    const availableModels = this.currentProvider.models;

    if (!modelName) {
      const { selectedModel } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedModel',
          message: 'Select a model:',
          choices: availableModels,
        },
      ]);
      modelName = selectedModel;
    }

    if (!modelName || !availableModels.includes(modelName)) {
      this.cli.displayError(`Model not available: ${modelName || 'undefined'}`);
      return;
    }

    this.configManager.updateCLIConfig({ model: modelName });
    this.context.config = this.configManager.getCLIConfig();
    
    this.cli.updateState({ currentModel: modelName });
    this.cli.displayMessage(`Switched to model: ${modelName}`, 'success');
  }

  private async handleSwitchProvider(providerName?: string): Promise<void> {
    const availableProviders = Array.from(this.providers.keys());

    if (!providerName) {
      const { selectedProvider } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedProvider',
          message: 'Select a provider:',
          choices: availableProviders,
        },
      ]);
      providerName = selectedProvider;
    }

    if (!providerName || !this.providers.has(providerName)) {
      this.cli.displayError(`Provider not available: ${providerName || 'undefined'}`);
      return;
    }

    try {
      this.configManager.setCurrentProvider(providerName);
      await this.initializeCurrentProvider();
      this.cli.displayMessage(`Switched to provider: ${providerName}`, 'success');
    } catch (error) {
      this.cli.displayError(`Failed to switch provider: ${(error as Error).message}`);
    }
  }

  private handleConfigCommand(args: string[]): void {
    if (args.length === 0) {
      const config = this.configManager.getCLIConfig();
      console.log(JSON.stringify(config, null, 2));
    } else {
      this.cli.displayMessage('Configuration modification not implemented yet', 'warning');
    }
  }

  private showConversationHistory(): void {
    if (this.conversationHistory.length === 0) {
      this.cli.displayMessage('No conversation history', 'info');
      return;
    }

    console.log(chalk.cyan('\nConversation History:'));
    this.conversationHistory.forEach((message, index) => {
      const roleColor = message.role === 'user' ? chalk.green : 
                       message.role === 'assistant' ? chalk.blue : chalk.gray;
      console.log(`${index + 1}. ${roleColor(message.role)}: ${message.content.substring(0, 100)}...`);
    });
    console.log();
  }

  private handleShowTools(toolName?: string): void {
    if (toolName) {
      const documentation = this.toolManager.getToolDocumentation(toolName);
      console.log(documentation);
    } else {
      this.listTools();
    }
  }

  private handleStreamInterruption(): void {
    this.cli.displayMessage('Stream interrupted by user', 'warning');
    // Add any cleanup logic here if needed
  }

  private async handleErrorWithRetry(error: Error, originalMessage: string): Promise<void> {
    this.cli.displayError(`Error occurred: ${error.message}`);

    // Implement "never give up" logic with alternative approaches
    const retryStrategies = [
      'Trying with simplified request...',
      'Attempting alternative approach...',
      'Using fallback method...'
    ];

    for (let i = 0; i < retryStrategies.length; i++) {
      try {
        this.cli.displayMessage(retryStrategies[i]!, 'warning');

        // Try with a simpler system prompt
        const simplifiedPrompt = `You are a helpful AI assistant. Please respond to: ${originalMessage}`;
        const messages: ChatMessage[] = [
          { role: 'system', content: simplifiedPrompt },
          { role: 'user', content: originalMessage }
        ];

        const response = await withAIRetry(async () => {
          return await this.currentProvider!.chat(messages, {
            temperature: 0.7,
            maxTokens: 2000,
          });
        });

        this.cli.displayAIResponse(response.content);

        // Add to history
        this.conversationHistory.push({ role: 'assistant', content: response.content });

        this.cli.displayMessage('Successfully recovered from error!', 'success');
        return;

      } catch (retryError) {
        this.cli.displayError(`Retry ${i + 1} failed: ${(retryError as Error).message}`);

        if (i === retryStrategies.length - 1) {
          this.cli.displayError('All retry attempts failed. Please try a different approach or check your configuration.');
          this.cli.displayMessage('Suggestions:', 'info');
          this.cli.displayMessage('1. Check your internet connection', 'info');
          this.cli.displayMessage('2. Verify your API keys are valid', 'info');
          this.cli.displayMessage('3. Try switching providers with /provider', 'info');
          this.cli.displayMessage('4. Simplify your request', 'info');
        }
      }
    }
  }

  close(): void {
    this.cli.close();
  }
}
