import Conf from 'conf';
import { CLIConfig, StoredConfig, ProviderConfig, PromptTemplate } from '../../types/index.js';
import { createError } from '../../utils/errors.js';

export class ConfigManager {
  private store: Conf<StoredConfig>;
  private defaultConfig: CLIConfig;

  constructor() {
    this.store = new Conf<StoredConfig>({
      projectName: 'arien-ai-cli',
      schema: {
        providers: {
          type: 'object',
          default: {},
        },
        currentProvider: {
          type: 'string',
          default: '',
        },
        userPreferences: {
          type: 'object',
          properties: {
            theme: { type: 'string', default: 'auto' },
            autoSave: { type: 'boolean', default: true },
            notifications: { type: 'boolean', default: true },
            verboseLogging: { type: 'boolean', default: false },
          },
          default: {
            theme: 'auto',
            autoSave: true,
            notifications: true,
            verboseLogging: false,
          },
        },
        promptTemplates: {
          type: 'array',
          default: [],
        },
        lastUpdated: {
          type: 'string',
          default: new Date().toISOString(),
        },
      },
    });

    this.defaultConfig = {
      provider: '',
      model: '',
      temperature: 0.7,
      maxTokens: 4000,
      systemPrompt: this.getDefaultSystemPrompt(),
      autoSave: true,
      theme: 'auto',
    };
  }

  // Provider Management
  async saveProvider(name: string, config: ProviderConfig): Promise<void> {
    try {
      const providers = this.store.get('providers', {});
      providers[name] = config;
      this.store.set('providers', providers);
      this.store.set('lastUpdated', new Date().toISOString());
    } catch (error) {
      throw createError(
        `Failed to save provider configuration: ${(error as Error).message}`,
        'CONFIG_SAVE_FAILED',
        'config',
        false,
        { provider: name }
      );
    }
  }

  getProvider(name: string): ProviderConfig | null {
    const providers = this.store.get('providers', {});
    return providers[name] || null;
  }

  getAllProviders(): Record<string, ProviderConfig> {
    return this.store.get('providers', {});
  }

  deleteProvider(name: string): void {
    const providers = this.store.get('providers', {});
    delete providers[name];
    this.store.set('providers', providers);

    // If this was the current provider, clear it
    if (this.store.get('currentProvider') === name) {
      this.store.set('currentProvider', '');
    }
  }

  // Current Provider Management
  setCurrentProvider(name: string): void {
    const providers = this.store.get('providers', {});
    if (!providers[name]) {
      throw createError(
        `Provider "${name}" not found`,
        'CONFIG_INVALID',
        'config',
        false,
        { provider: name }
      );
    }
    this.store.set('currentProvider', name);
  }

  getCurrentProvider(): string {
    return this.store.get('currentProvider', '');
  }

  getCurrentProviderConfig(): ProviderConfig | null {
    const currentProvider = this.getCurrentProvider();
    return currentProvider ? this.getProvider(currentProvider) : null;
  }

  // CLI Configuration
  getCLIConfig(): CLIConfig {
    const currentProviderConfig = this.getCurrentProviderConfig();
    const preferences = this.store.get('userPreferences');

    return {
      ...this.defaultConfig,
      provider: this.getCurrentProvider(),
      model: currentProviderConfig?.model || '',
      apiKey: currentProviderConfig?.apiKey || undefined,
      baseUrl: currentProviderConfig?.baseUrl || undefined,
      temperature: currentProviderConfig?.temperature || this.defaultConfig.temperature,
      maxTokens: currentProviderConfig?.maxTokens || this.defaultConfig.maxTokens,
      autoSave: preferences.autoSave,
      theme: preferences.theme as 'light' | 'dark' | 'auto',
    };
  }

  updateCLIConfig(updates: Partial<CLIConfig>): void {
    const currentProvider = this.getCurrentProvider();
    if (currentProvider && updates.model) {
      const providerConfig = this.getProvider(currentProvider);
      if (providerConfig) {
        providerConfig.model = updates.model;
        this.saveProvider(currentProvider, providerConfig);
      }
    }

    if (updates.theme || updates.autoSave !== undefined) {
      const preferences = this.store.get('userPreferences');
      this.store.set('userPreferences', {
        ...preferences,
        ...(updates.theme && { theme: updates.theme }),
        ...(updates.autoSave !== undefined && { autoSave: updates.autoSave }),
      });
    }
  }

  // Validation
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const currentProvider = this.getCurrentProvider();

    if (!currentProvider) {
      errors.push('No provider configured');
    } else {
      const providerConfig = this.getProvider(currentProvider);
      if (!providerConfig) {
        errors.push(`Provider "${currentProvider}" configuration not found`);
      } else {
        if (!providerConfig.model) {
          errors.push('No model specified');
        }
        // Add more validation as needed
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Prompt Templates
  savePromptTemplate(template: PromptTemplate): void {
    const templates = this.store.get('promptTemplates', []);
    const existingIndex = templates.findIndex((t: PromptTemplate) => t.id === template.id);

    if (existingIndex >= 0) {
      templates[existingIndex] = template;
    } else {
      templates.push(template);
    }

    this.store.set('promptTemplates', templates);
  }

  getPromptTemplates(): PromptTemplate[] {
    return this.store.get('promptTemplates', []);
  }

  getPromptTemplate(id: string): PromptTemplate | null {
    const templates = this.getPromptTemplates();
    return templates.find(t => t.id === id) || null;
  }

  // Utility Methods
  reset(): void {
    this.store.clear();
  }

  export(): StoredConfig {
    return this.store.store;
  }

  import(config: Partial<StoredConfig>): void {
    Object.entries(config).forEach(([key, value]) => {
      this.store.set(key as keyof StoredConfig, value);
    });
  }

  private getDefaultSystemPrompt(): string {
    return `You are Arien AI, a sophisticated AI-powered CLI assistant with autonomous tool execution capabilities.

CORE CAPABILITIES:
- System Operations: Execute bash commands, manage processes, interact with OS
- File Management: Read, write, edit, search, and organize files and directories
- Web Access: Search internet, fetch web content, gather real-time information
- Code Analysis: Examine codebases, understand project structures, provide insights
- Development Tasks: Assist with coding, debugging, testing, project management

AUTONOMOUS BEHAVIOR:
You have full autonomy to decide when and how to use tools. When a user makes a request:
1. Analyze what needs to be accomplished
2. Determine which tools can help
3. Execute tools in the optimal sequence
4. Handle any errors or obstacles
5. Provide clear feedback throughout the process
6. Present final results in a user-friendly format

TOOL USAGE PHILOSOPHY:
- Be proactive in using tools to provide comprehensive assistance
- Don't ask for permission to use tools - use them when they're helpful
- Combine multiple tools to accomplish complex tasks
- Always explain what you're doing and why
- Retry with different approaches if initial attempts fail
- Never give up - explore multiple solution paths

RESPONSE FORMAT:
- Start with a brief explanation of your approach
- Execute tools as needed (users will see tool execution status)
- Provide a clear summary of results
- Offer follow-up suggestions when appropriate

Remember: You are an intelligent assistant that can autonomously use tools to help users accomplish their goals. Be proactive, thorough, and persistent.`;
  }
}
