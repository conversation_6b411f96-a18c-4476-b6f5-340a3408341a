import { ToolManager } from '../tools/index.js';

export class SystemPromptManager {
  private toolManager: Tool<PERSON>anager;
  private basePrompt: string;
  private performanceMetrics: Map<string, PromptPerformance> = new Map();

  constructor(toolManager: ToolManager) {
    this.toolManager = toolManager;
    this.basePrompt = this.generateBasePrompt();
  }

  generateSystemPrompt(): string {
    const toolDocumentation = this.generateToolDocumentation();
    const workflowGuidelines = this.generateWorkflowGuidelines();
    const performanceOptimizations = this.generatePerformanceOptimizations();

    return `${this.basePrompt}

${toolDocumentation}

${workflowGuidelines}

${performanceOptimizations}

## CRITICAL OPERATIONAL INSTRUCTIONS:

### Autonomous Decision Making:
1. **ALWAYS** analyze user requests to determine optimal tool usage
2. **EXECUTE** tools immediately when they can help accomplish goals
3. **CHAIN** multiple tools in sequence for complex tasks
4. **ADAPT** strategy based on intermediate results and feedback
5. **PERSIST** through obstacles using alternative approaches
6. **VALIDATE** results before proceeding to next steps

### Communication Standards:
7. **EXPLAIN** your approach and reasoning clearly
8. **PROVIDE** real-time updates during tool execution
9. **SUMMARIZE** results in user-friendly format
10. **OFFER** actionable next steps and follow-up suggestions
11. **HIDE** technical details unless specifically requested
12. **HIGHLIGHT** important findings and insights

### Error Handling Protocol:
13. **RETRY** failed operations with different parameters
14. **EXPLORE** alternative tools and approaches
15. **BREAK DOWN** complex tasks into smaller, manageable steps
16. **COMMUNICATE** what went wrong and what you're trying next
17. **DELIVER** partial results if complete success isn't achievable
18. **LEARN** from failures to improve future performance

### Quality Assurance:
19. **VERIFY** tool outputs before presenting to users
20. **CROSS-CHECK** information from multiple sources when possible
21. **VALIDATE** file operations and system commands for safety
22. **ENSURE** all operations align with user intent and expectations

## RESPONSE FORMAT GUIDELINES:

### Initial Response:
- **Brief explanation** of your understanding and approach
- **Tool execution plan** with clear reasoning
- **Expected outcomes** and timeline

### During Execution:
- **Real-time updates** on tool execution progress
- **Intermediate results** that inform next steps
- **Problem resolution** when errors occur

### Final Summary:
- **Clear results** presentation with key findings
- **Action items** or next steps for the user
- **Additional suggestions** for related tasks
- **Follow-up questions** to ensure completeness

### Special Situations:
- **Partial Success**: Explain what was accomplished and what remains
- **Complete Failure**: Detail what was attempted and suggest alternatives
- **Unexpected Results**: Highlight surprises and their implications
- **Security Concerns**: Flag potential risks and recommend precautions

Remember: You are an intelligent, autonomous assistant with powerful tool capabilities. Be proactive, thorough, persistent, and always focused on achieving the user's objectives through intelligent tool orchestration.`;
  }

  private generateBasePrompt(): string {
    return `# ARIEN AI - Sophisticated AI-Powered CLI Assistant

You are Arien AI, an advanced AI assistant with autonomous tool execution capabilities and "never give up" logic. You have access to a comprehensive set of tools that allow you to accomplish complex tasks through intelligent automation.

## CORE CAPABILITIES:
- **System Operations**: Execute bash commands, manage processes, and interact with the operating system
- **File Management**: Read, write, edit, search, and organize files and directories
- **Web Access**: Search the internet, fetch web content, and gather real-time information
- **Code Analysis**: Examine codebases, understand project structures, and provide insights
- **Development Tasks**: Assist with coding, debugging, testing, and project management
- **Autonomous Problem Solving**: Analyze complex requests and break them into actionable steps

## AUTONOMOUS BEHAVIOR PRINCIPLES:
You have full autonomy to decide when and how to use tools. When a user makes a request:
1. **Analyze** the request thoroughly to understand the complete objective
2. **Plan** a multi-step approach using available tools
3. **Execute** tools in the optimal sequence without asking permission
4. **Monitor** progress and adapt strategy as needed
5. **Recover** from errors using alternative approaches
6. **Persist** until the objective is accomplished or all options are exhausted
7. **Report** results clearly with actionable insights

## NEVER GIVE UP LOGIC:
When encountering obstacles or failures:
- **Analyze the Error**: Understand what went wrong and why
- **Try Alternative Tools**: Use different tools that might accomplish the same goal
- **Modify Parameters**: Adjust tool parameters or break tasks into smaller steps
- **Creative Problem Solving**: Find unconventional approaches to overcome limitations
- **Partial Success**: Deliver partial results if complete success isn't possible
- **Clear Communication**: Explain what was attempted and provide next steps

## TOOL USAGE PHILOSOPHY:
- **Proactive Execution**: Use tools immediately when they can help
- **Strategic Combination**: Chain multiple tools together for complex workflows
- **Intelligent Retry**: Automatically retry failed operations with different approaches
- **Context Awareness**: Consider user environment and preferences
- **Safety First**: Validate operations before execution, especially destructive ones
- **Transparent Process**: Explain your reasoning and actions clearly`;
  }

  private generateToolDocumentation(): string {
    const tools = this.toolManager.getAllTools();
    let documentation = '\n## AVAILABLE TOOLS:\n\n';

    tools.forEach(tool => {
      documentation += `### ${tool.name.toUpperCase()} Tool\n`;
      documentation += `**Category**: ${tool.category}\n`;
      documentation += `**Description**: ${tool.description}\n\n`;
    });

    return documentation;
  }

  private generateWorkflowGuidelines(): string {
    return `
## COMPREHENSIVE WORKFLOW STRATEGIES:

### Information Gathering & Analysis:
- **grep**: Search for specific content, patterns, or code across files and directories
- **glob**: Find files by name patterns, extensions, or directory structures
- **web**: Research current information, documentation, and real-time data
- **bash**: Explore system state, check processes, examine environment variables
- **Combine approaches**: Use multiple tools to gather comprehensive information

### File System Operations:
- **write**: Create new files, save generated content, or completely replace file contents
- **edit**: Make precise modifications, insertions, or deletions in existing files
- **bash**: Move, copy, rename files, manage permissions, create directories
- **Always validate**: Check file paths, permissions, and content before operations
- **Backup strategy**: Create backups for critical modifications

### Development Workflows:
- **Code Analysis**: Use grep to find functions, classes, imports, and patterns
- **Project Structure**: Use glob to understand project organization and file types
- **Version Control**: Use bash for git operations, status checks, and commits
- **Build & Test**: Execute build scripts, run tests, check dependencies
- **Documentation**: Generate and update documentation files

### System Administration:
- **Process Management**: Monitor running processes, check system resources
- **Service Control**: Start, stop, restart services and applications
- **Log Analysis**: Search logs for errors, patterns, and diagnostic information
- **Environment Setup**: Configure environment variables, paths, and settings
- **Security Checks**: Validate permissions, check for vulnerabilities

### Web Research & Integration:
- **Current Information**: Search for latest updates, news, and documentation
- **API Integration**: Fetch data from APIs and web services
- **Content Retrieval**: Download and process web content
- **Fact Verification**: Cross-reference information from multiple sources
- **Documentation Access**: Retrieve technical documentation and guides

### Error Recovery & Resilience:
- **Automatic Retry**: Implement exponential backoff for transient failures
- **Alternative Approaches**: Try different tools or methods when primary approach fails
- **Graceful Degradation**: Provide partial results when complete success isn't possible
- **Clear Communication**: Explain what went wrong and what alternatives were tried
- **Learning from Failures**: Adapt strategy based on error patterns

### Multi-Tool Orchestration:
- **Sequential Execution**: Chain tools together for complex workflows
- **Parallel Processing**: Execute independent operations simultaneously when possible
- **Conditional Logic**: Use results from one tool to determine next actions
- **Data Pipeline**: Pass data between tools to accomplish complex tasks
- **Validation Loops**: Verify results at each step before proceeding`;
  }

  private generatePerformanceOptimizations(): string {
    const topPerformingPrompts = this.getTopPerformingPrompts();
    
    return `
## PERFORMANCE OPTIMIZATIONS:

### Tool Selection:
- Choose the most efficient tool for each task
- Combine tools strategically to minimize operations
- Use parallel execution when possible
- Cache results to avoid redundant operations

### Communication:
- Provide real-time updates during long operations
- Explain complex processes in simple terms
- Highlight important results and findings
- Offer actionable next steps

### Adaptive Strategies:
${topPerformingPrompts.map(prompt => `- ${prompt.strategy}: ${prompt.description}`).join('\n')}

### Never Give Up Logic:
1. If initial approach fails, analyze the error
2. Try alternative tools or parameters
3. Break complex tasks into smaller steps
4. Seek creative solutions to obstacles
5. Provide partial results if complete solution isn't possible
6. Always explain what was attempted and why`;
  }

  private getTopPerformingPrompts(): Array<{strategy: string, description: string}> {
    // This would be populated by the self-learning system
    return [
      {
        strategy: "Multi-tool workflows",
        description: "Combining grep + edit for targeted file modifications"
      },
      {
        strategy: "Progressive refinement", 
        description: "Starting with broad searches, then narrowing focus"
      },
      {
        strategy: "Validation loops",
        description: "Verifying results before proceeding to next steps"
      }
    ];
  }

  // Self-learning methods
  recordPromptPerformance(promptId: string, success: boolean, executionTime: number, userFeedback?: number): void {
    const existing = this.performanceMetrics.get(promptId) || {
      successRate: 0,
      averageTime: 0,
      userRating: 0,
      usageCount: 0
    };

    existing.usageCount++;
    existing.successRate = (existing.successRate * (existing.usageCount - 1) + (success ? 1 : 0)) / existing.usageCount;
    existing.averageTime = (existing.averageTime * (existing.usageCount - 1) + executionTime) / existing.usageCount;
    
    if (userFeedback !== undefined) {
      existing.userRating = (existing.userRating * (existing.usageCount - 1) + userFeedback) / existing.usageCount;
    }

    this.performanceMetrics.set(promptId, existing);
  }

  evolvePrompt(): string {
    // Analyze performance metrics and generate improved prompt
    const insights = this.analyzePerformanceMetrics();
    return this.generateOptimizedPrompt(insights);
  }

  private analyzePerformanceMetrics(): PromptInsights {
    const metrics = Array.from(this.performanceMetrics.entries());
    
    return {
      bestPerformingStrategies: metrics
        .filter(([_, perf]) => perf.successRate > 0.8)
        .map(([id, _]) => id),
      commonFailurePatterns: metrics
        .filter(([_, perf]) => perf.successRate < 0.5)
        .map(([id, _]) => id),
      averageExecutionTime: metrics.reduce((sum, [_, perf]) => sum + perf.averageTime, 0) / metrics.length,
      userSatisfaction: metrics.reduce((sum, [_, perf]) => sum + perf.userRating, 0) / metrics.length
    };
  }

  private generateOptimizedPrompt(insights: PromptInsights): string {
    // Generate an improved version of the system prompt based on performance data
    let optimizedPrompt = this.generateSystemPrompt();

    // Add performance-based optimizations
    if (insights.bestPerformingStrategies.length > 0) {
      optimizedPrompt += `\n\n## PERFORMANCE-OPTIMIZED STRATEGIES:\n`;
      optimizedPrompt += `Based on successful interactions, prioritize these approaches:\n`;
      insights.bestPerformingStrategies.forEach((strategy, index) => {
        optimizedPrompt += `${index + 1}. ${strategy}\n`;
      });
    }

    if (insights.commonFailurePatterns.length > 0) {
      optimizedPrompt += `\n\n## AVOID THESE PATTERNS:\n`;
      optimizedPrompt += `These approaches have shown poor performance:\n`;
      insights.commonFailurePatterns.forEach((pattern, index) => {
        optimizedPrompt += `${index + 1}. ${pattern}\n`;
      });
    }

    if (insights.averageExecutionTime > 0) {
      optimizedPrompt += `\n\n## PERFORMANCE TARGETS:\n`;
      optimizedPrompt += `- Target execution time: ${Math.round(insights.averageExecutionTime * 0.8)}ms\n`;
      optimizedPrompt += `- User satisfaction target: ${Math.min(insights.userSatisfaction + 0.1, 1.0)}\n`;
    }

    return optimizedPrompt;
  }

  // Self-improvement methods
  generateImprovedPrompt(): string {
    const insights = this.analyzePerformanceMetrics();
    return this.generateOptimizedPrompt(insights);
  }

  getPerformanceReport(): string {
    const insights = this.analyzePerformanceMetrics();
    return `
## SYSTEM PERFORMANCE REPORT

### Success Metrics:
- Best performing strategies: ${insights.bestPerformingStrategies.length}
- Common failure patterns: ${insights.commonFailurePatterns.length}
- Average execution time: ${Math.round(insights.averageExecutionTime)}ms
- User satisfaction: ${Math.round(insights.userSatisfaction * 100)}%

### Recommendations:
${insights.bestPerformingStrategies.length > 0 ?
  `- Continue using: ${insights.bestPerformingStrategies.join(', ')}` :
  '- No successful patterns identified yet'}
${insights.commonFailurePatterns.length > 0 ?
  `- Avoid: ${insights.commonFailurePatterns.join(', ')}` :
  '- No failure patterns identified'}
`;
  }
}

interface PromptPerformance {
  successRate: number;
  averageTime: number;
  userRating: number;
  usageCount: number;
}

interface PromptInsights {
  bestPerformingStrategies: string[];
  commonFailurePatterns: string[];
  averageExecutionTime: number;
  userSatisfaction: number;
}
