import { exec } from 'child_process';
import { promisify } from 'util';
import { Tool, FunctionResult } from '../../types/index.js';
import { createError, ErrorCodes } from '../../utils/errors.js';

const execAsync = promisify(exec);

export class BashTool implements Tool {
  public readonly name = 'bash';
  public readonly description = 'Runs and executes all types of bash commands in the interactive shell. Supports command execution with optional working directory specification.';
  public readonly category = 'system' as const;

  async execute(args: Record<string, unknown>): Promise<FunctionResult> {
    try {
      const { command, workingDirectory } = args;

      if (typeof command !== 'string') {
        return {
          success: false,
          error: 'Command must be a string',
        };
      }

      // Security check - prevent dangerous commands
      if (this.isDangerousCommand(command)) {
        return {
          success: false,
          error: 'Command rejected for security reasons',
        };
      }

      const options: any = {
        timeout: 30000, // 30 second timeout
        maxBuffer: 1024 * 1024, // 1MB buffer
      };

      if (workingDirectory && typeof workingDirectory === 'string') {
        options.cwd = workingDirectory;
      }

      const { stdout, stderr } = await execAsync(command, options);

      return {
        success: true,
        result: {
          stdout: stdout.toString().trim(),
          stderr: stderr.toString().trim(),
          command,
          workingDirectory: options.cwd || process.cwd(),
        },
        output: stdout.toString().trim() || stderr.toString().trim() || 'Command executed successfully',
      };
    } catch (error: any) {
      const errorMessage = error.message || 'Unknown error';
      const exitCode = error.code;
      const signal = error.signal;

      return {
        success: false,
        error: `Command failed: ${errorMessage}`,
        result: {
          exitCode,
          signal,
          stdout: error.stdout || '',
          stderr: error.stderr || '',
        },
      };
    }
  }

  validate(args: Record<string, unknown>): boolean {
    return typeof args.command === 'string' && args.command.trim().length > 0;
  }

  private isDangerousCommand(command: string): boolean {
    const dangerousPatterns = [
      /rm\s+-rf\s+\//, // rm -rf /
      /rm\s+-rf\s+\*/, // rm -rf *
      /:\(\)\{.*\}/, // Fork bomb pattern
      /sudo\s+rm/, // sudo rm
      /mkfs/, // Format filesystem
      /dd\s+if=.*of=\/dev/, // Direct disk write
      /shutdown/, // System shutdown
      /reboot/, // System reboot
      /halt/, // System halt
      /init\s+0/, // System shutdown
      /init\s+6/, // System reboot
      /\/dev\/sd[a-z]/, // Direct disk access
      /\/dev\/hd[a-z]/, // Direct disk access
      /chmod\s+777\s+\//, // Dangerous permissions on root
    ];

    return dangerousPatterns.some(pattern => pattern.test(command.toLowerCase()));
  }

  getDocumentation(): string {
    return `
# BASH Tool

**Category:** System
**Description:** Runs and executes all types of bash commands in the interactive shell

## Primary Use Cases:
- Execute system commands and scripts
- File system operations (ls, find, etc.)
- Process management and monitoring
- Environment inspection
- Development tasks (git, npm, etc.)

## When NOT to Use:
- For destructive operations without user confirmation
- Commands that require interactive input
- Long-running processes (use with caution due to timeout)
- Commands that modify critical system files

## Parameters:
- **command** (required): The bash command to execute
- **workingDirectory** (optional): Working directory for command execution

## Security Considerations:
- Commands are validated against dangerous patterns
- 30-second timeout prevents hanging processes
- Output buffer limited to 1MB
- Certain destructive commands are blocked

## Best Practices:
- Always validate command safety before execution
- Use absolute paths when possible
- Handle both stdout and stderr in responses
- Consider timeout implications for long-running commands

## Performance Considerations:
- Commands timeout after 30 seconds
- Output limited to 1MB to prevent memory issues
- Each execution creates a new shell process

## Examples:
\`\`\`json
{"command": "ls -la"}
{"command": "git status", "workingDirectory": "/path/to/repo"}
{"command": "npm install", "workingDirectory": "./project"}
\`\`\`
`;
  }
}
