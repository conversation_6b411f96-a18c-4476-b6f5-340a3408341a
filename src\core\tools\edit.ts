import { readFile, writeFile, access, constants } from 'fs/promises';
import { Tool, FunctionResult } from '../../types/index.js';

export class EditTool implements Tool {
  public readonly name = 'edit';
  public readonly description = 'Edits files by replacing text, creating new files, or deleting content. For moving or renaming files, use the Bash tool with the mv command instead. For larger file edits, use the FileWrite tool to overwrite files';
  public readonly category = 'file' as const;

  async execute(args: Record<string, unknown>): Promise<FunctionResult> {
    try {
      const { path, operation, content, lineNumber, searchText, replaceText, encoding = 'utf8' } = args;

      if (typeof path !== 'string' || typeof operation !== 'string') {
        return {
          success: false,
          error: 'Path and operation must be strings',
        };
      }

      // Check if file exists for operations that require it
      const fileExists = await this.fileExists(path);
      
      switch (operation) {
        case 'replace':
          return await this.replaceText(path, searchText as string, replaceText as string, encoding as string, fileExists);
        
        case 'insert':
          return await this.insertLine(path, lineNumber as number, content as string, encoding as string, fileExists);
        
        case 'delete':
          return await this.deleteLine(path, lineNumber as number, encoding as string, fileExists);
        
        case 'create':
          return await this.createFile(path, content as string, encoding as string);
        
        default:
          return {
            success: false,
            error: `Unknown operation: ${operation}`,
          };
      }
    } catch (error: any) {
      return {
        success: false,
        error: `Edit operation failed: ${error.message}`,
      };
    }
  }

  validate(args: Record<string, unknown>): boolean {
    const { path, operation } = args;
    
    if (typeof path !== 'string' || typeof operation !== 'string') {
      return false;
    }

    switch (operation) {
      case 'replace':
        return typeof args.searchText === 'string' && typeof args.replaceText === 'string';
      case 'insert':
        return typeof args.lineNumber === 'number' && typeof args.content === 'string';
      case 'delete':
        return typeof args.lineNumber === 'number';
      case 'create':
        return typeof args.content === 'string';
      default:
        return false;
    }
  }

  private async fileExists(path: string): Promise<boolean> {
    try {
      await access(path, constants.F_OK);
      return true;
    } catch {
      return false;
    }
  }

  private async replaceText(
    path: string, 
    searchText: string, 
    replaceText: string, 
    encoding: string,
    fileExists: boolean
  ): Promise<FunctionResult> {
    if (!fileExists) {
      return {
        success: false,
        error: `File does not exist: ${path}`,
      };
    }

    const content = await readFile(path, encoding as BufferEncoding);
    const originalLength = content.length;
    
    // Perform replacement
    const newContent = content.replace(new RegExp(this.escapeRegex(searchText), 'g'), replaceText);
    const replacements = (content.match(new RegExp(this.escapeRegex(searchText), 'g')) || []).length;
    
    if (replacements === 0) {
      return {
        success: false,
        error: `Search text not found: "${searchText}"`,
      };
    }

    await writeFile(path, newContent, encoding as BufferEncoding);

    return {
      success: true,
      result: {
        path,
        operation: 'replace',
        searchText,
        replaceText,
        replacements,
        originalLength,
        newLength: newContent.length,
      },
      output: `Replaced ${replacements} occurrence(s) of "${searchText}" with "${replaceText}"`,
    };
  }

  private async insertLine(
    path: string, 
    lineNumber: number, 
    content: string, 
    encoding: string,
    fileExists: boolean
  ): Promise<FunctionResult> {
    let lines: string[] = [];
    
    if (fileExists) {
      const fileContent = await readFile(path, encoding as BufferEncoding);
      lines = fileContent.split('\n');
    }

    // Insert at specified line (1-based indexing)
    const insertIndex = Math.max(0, Math.min(lineNumber - 1, lines.length));
    lines.splice(insertIndex, 0, content);

    const newContent = lines.join('\n');
    await writeFile(path, newContent, encoding as BufferEncoding);

    return {
      success: true,
      result: {
        path,
        operation: 'insert',
        lineNumber: insertIndex + 1,
        content,
        totalLines: lines.length,
      },
      output: `Inserted line at position ${insertIndex + 1}`,
    };
  }

  private async deleteLine(
    path: string, 
    lineNumber: number, 
    encoding: string,
    fileExists: boolean
  ): Promise<FunctionResult> {
    if (!fileExists) {
      return {
        success: false,
        error: `File does not exist: ${path}`,
      };
    }

    const fileContent = await readFile(path, encoding as BufferEncoding);
    const lines = fileContent.split('\n');

    if (lineNumber < 1 || lineNumber > lines.length) {
      return {
        success: false,
        error: `Line number ${lineNumber} is out of range (1-${lines.length})`,
      };
    }

    const deletedContent = lines[lineNumber - 1];
    lines.splice(lineNumber - 1, 1);

    const newContent = lines.join('\n');
    await writeFile(path, newContent, encoding as BufferEncoding);

    return {
      success: true,
      result: {
        path,
        operation: 'delete',
        lineNumber,
        deletedContent,
        totalLines: lines.length,
      },
      output: `Deleted line ${lineNumber}`,
    };
  }

  private async createFile(path: string, content: string, encoding: string): Promise<FunctionResult> {
    const fileExists = await this.fileExists(path);
    
    if (fileExists) {
      return {
        success: false,
        error: `File already exists: ${path}. Use write tool to overwrite or replace operation to modify.`,
      };
    }

    await writeFile(path, content, encoding as BufferEncoding);

    return {
      success: true,
      result: {
        path,
        operation: 'create',
        contentLength: content.length,
      },
      output: `Created new file: ${path}`,
    };
  }

  private escapeRegex(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  getDocumentation(): string {
    return `
# EDIT Tool

**Category:** File
**Description:** Edits files by replacing text, creating new files, or deleting content

## Primary Use Cases:
- Precise text replacements in existing files
- Insert new lines at specific positions
- Delete specific lines from files
- Create new files with initial content
- Make targeted modifications without rewriting entire files

## When NOT to Use:
- For moving or renaming files (use bash tool with mv command)
- For large file rewrites (use write tool to overwrite)
- For binary file modifications
- For complex multi-step edits (consider write tool)

## Operations:
### replace
- **searchText** (required): Text to find and replace
- **replaceText** (required): Replacement text
- Supports global replacement of all occurrences

### insert
- **lineNumber** (required): Line position to insert at (1-based)
- **content** (required): Content to insert

### delete
- **lineNumber** (required): Line number to delete (1-based)

### create
- **content** (required): Initial file content

## Best Practices:
- Use specific search text to avoid unintended replacements
- Validate line numbers before delete/insert operations
- Consider file backup before major edits
- Test replacements on small files first
- Use create operation only for new files

## Performance Considerations:
- Entire file is loaded into memory
- Large files may impact performance
- Multiple edits should be batched when possible
- Line-based operations are efficient for text files

## Integration Notes:
- Perfect complement to grep tool for find-and-replace workflows
- Works well with glob tool for batch file editing
- Use after write tool for fine-tuned modifications
- Combine with bash tool for file management operations

## Examples:
\`\`\`json
{"path": "file.txt", "operation": "replace", "searchText": "old", "replaceText": "new"}
{"path": "file.txt", "operation": "insert", "lineNumber": 5, "content": "new line"}
{"path": "file.txt", "operation": "delete", "lineNumber": 3}
{"path": "new.txt", "operation": "create", "content": "Hello World"}
\`\`\`

## Error Handling:
- Validates file existence for required operations
- Checks line number bounds for insert/delete
- Provides clear feedback on search text not found
- Prevents overwriting existing files with create operation
`;
  }
}
