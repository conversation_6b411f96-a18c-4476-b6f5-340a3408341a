import { glob } from 'glob';
import { stat } from 'fs/promises';
import { Tool, FunctionResult } from '../../types/index.js';

export class GlobTool implements Tool {
  public readonly name = 'glob';
  public readonly description = 'Fast file pattern matching tool that finds files by name and pattern, returning matching paths sorted by modification time (newest first)';
  public readonly category = 'file' as const;

  async execute(args: Record<string, unknown>): Promise<FunctionResult> {
    try {
      const { pattern, path = '.', maxResults = 100, includeDirectories = false } = args;

      if (typeof pattern !== 'string') {
        return {
          success: false,
          error: 'Pattern must be a string',
        };
      }

      const searchPattern = this.buildSearchPattern(pattern, path as string);
      const globOptions = {
        dot: false, // Don't include hidden files by default
        ignore: [
          'node_modules/**',
          '.git/**',
          'dist/**',
          'build/**',
          '*.log',
          '.DS_Store',
        ],
        maxDepth: 10, // Prevent infinite recursion
      };

      const matches = await glob(searchPattern, globOptions);

      // Filter directories if not requested
      let filteredMatches = matches;
      if (!includeDirectories) {
        const fileChecks = await Promise.allSettled(
          matches.map(async (match) => {
            const stats = await stat(match);
            return { path: match, isFile: stats.isFile() };
          })
        );

        filteredMatches = fileChecks
          .filter((result): result is PromiseFulfilledResult<{ path: string; isFile: boolean }> => 
            result.status === 'fulfilled' && result.value.isFile
          )
          .map(result => result.value.path);
      }

      // Sort by modification time (newest first)
      const sortedMatches = await this.sortByModificationTime(
        filteredMatches.slice(0, maxResults as number)
      );

      return {
        success: true,
        result: {
          pattern,
          searchPath: path,
          matches: sortedMatches,
          totalMatches: sortedMatches.length,
          searchOptions: {
            includeDirectories,
            maxResults,
          },
        },
        output: sortedMatches.length > 0
          ? `Found ${sortedMatches.length} files matching "${pattern}"`
          : `No files found matching "${pattern}"`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Pattern matching failed: ${error.message}`,
      };
    }
  }

  validate(args: Record<string, unknown>): boolean {
    return typeof args.pattern === 'string' && args.pattern.trim().length > 0;
  }

  private buildSearchPattern(pattern: string, basePath: string): string {
    // Normalize the base path
    const normalizedPath = basePath.replace(/\\/g, '/').replace(/\/$/, '');
    
    // If pattern already includes path separators, use as-is
    if (pattern.includes('/') || pattern.includes('\\')) {
      return pattern;
    }

    // For simple patterns, search recursively from base path
    if (normalizedPath === '.' || normalizedPath === '') {
      return `**/${pattern}`;
    }

    return `${normalizedPath}/**/${pattern}`;
  }

  private async sortByModificationTime(files: string[]): Promise<Array<{
    path: string;
    modifiedTime: Date;
    size: number;
  }>> {
    const fileStats = await Promise.allSettled(
      files.map(async (file) => {
        const stats = await stat(file);
        return {
          path: file,
          modifiedTime: stats.mtime,
          size: stats.size,
        };
      })
    );

    return fileStats
      .filter((result): result is PromiseFulfilledResult<{
        path: string;
        modifiedTime: Date;
        size: number;
      }> => result.status === 'fulfilled')
      .map(result => result.value)
      .sort((a, b) => b.modifiedTime.getTime() - a.modifiedTime.getTime());
  }

  getDocumentation(): string {
    return `
# GLOB Tool

**Category:** File
**Description:** Fast file pattern matching tool that finds files by name and pattern

## Primary Use Cases:
- Find files by extension (*.ts, *.js, *.json)
- Locate files with specific naming patterns
- Discover configuration files
- Find test files or documentation
- Batch file operations preparation

## When NOT to Use:
- For content-based searches (use grep tool instead)
- When you need exact file paths (use direct file access)
- For very specific regex patterns (glob patterns are simpler)
- When searching inside archives or compressed files

## Parameters:
- **pattern** (required): File pattern to match (supports wildcards)
- **path** (optional): Base directory to search in (default: current directory)
- **maxResults** (optional): Maximum number of results (default: 100)
- **includeDirectories** (optional): Include directories in results (default: false)

## Pattern Syntax Examples:
- \`*.ts\` - All TypeScript files
- \`**/*.js\` - All JavaScript files recursively
- \`test*.json\` - JSON files starting with "test"
- \`src/**/index.*\` - All index files in src directory
- \`{*.ts,*.js}\` - TypeScript OR JavaScript files
- \`[abc]*.txt\` - Text files starting with a, b, or c

## Best Practices:
- Use specific patterns to avoid too many results
- Consider performance impact of deep recursive searches
- Use maxResults to limit output for large codebases
- Combine with other tools for complex file operations

## Performance Considerations:
- Automatically ignores common build/cache directories
- Limited to 10 levels of directory depth
- Results sorted by modification time (newest first)
- File stat operations may be slow for many files

## Integration Notes:
- Perfect for preparing file lists for batch operations
- Works well with edit/write tools for file modifications
- Combine with grep for content + filename searches
- Output includes file metadata for additional filtering

## Examples:
\`\`\`json
{"pattern": "*.ts", "path": "./src"}
{"pattern": "**/*.test.js", "path": "."}
{"pattern": "config*.json", "maxResults": 10}
{"pattern": "*.{md,txt}", "includeDirectories": false}
\`\`\`

## Automatic Exclusions:
- node_modules/
- .git/
- dist/
- build/
- *.log files
- .DS_Store files
`;
  }
}
