import { Tool, FunctionDefinition, FunctionResult } from '../../types/index.js';
import { BashTool } from './bash.js';
import { GrepTool } from './grep.js';
import { GlobTool } from './glob.js';
import { WriteTool } from './write.js';
import { EditTool } from './edit.js';
import { WebTool } from './web.js';

export class ToolManager {
  private tools: Map<string, Tool> = new Map();

  constructor() {
    this.registerDefaultTools();
  }

  private registerDefaultTools(): void {
    const defaultTools = [
      new BashTool(),
      new GrepTool(),
      new GlobTool(),
      new WriteTool(),
      new EditTool(),
      new WebTool(),
    ];

    defaultTools.forEach(tool => this.registerTool(tool));
  }

  registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
  }

  unregisterTool(name: string): void {
    this.tools.delete(name);
  }

  getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  getAllTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  getToolsByCategory(category: Tool['category']): Tool[] {
    return this.getAllTools().filter(tool => tool.category === category);
  }

  async executeTool(name: string, args: Record<string, unknown>): Promise<FunctionResult> {
    const tool = this.getTool(name);
    if (!tool) {
      return {
        success: false,
        error: `Tool "${name}" not found`,
      };
    }

    try {
      // Validate arguments if the tool has validation
      if (tool.validate && !tool.validate(args)) {
        return {
          success: false,
          error: `Invalid arguments for tool "${name}"`,
        };
      }

      return await tool.execute(args);
    } catch (error) {
      return {
        success: false,
        error: `Tool execution failed: ${(error as Error).message}`,
      };
    }
  }

  getFunctionDefinitions(): FunctionDefinition[] {
    return this.getAllTools().map(tool => this.toolToFunctionDefinition(tool));
  }

  private toolToFunctionDefinition(tool: Tool): FunctionDefinition {
    // This is a basic conversion - each tool should ideally provide its own schema
    return {
      name: tool.name,
      description: tool.description,
      parameters: {
        type: 'object',
        properties: this.generateParametersForTool(tool),
        required: this.getRequiredParametersForTool(tool),
      },
    };
  }

  private generateParametersForTool(tool: Tool): Record<string, {
    type: string;
    description: string;
    enum?: string[];
  }> {
    // Basic parameter definitions - tools should provide their own schemas
    switch (tool.name) {
      case 'bash':
        return {
          command: {
            type: 'string',
            description: 'The bash command to execute',
          },
          workingDirectory: {
            type: 'string',
            description: 'Working directory for the command (optional)',
          },
        };

      case 'grep':
        return {
          pattern: {
            type: 'string',
            description: 'Text pattern or regex to search for',
          },
          path: {
            type: 'string',
            description: 'File or directory path to search in',
          },
          recursive: {
            type: 'boolean',
            description: 'Search recursively in directories',
          },
          caseInsensitive: {
            type: 'boolean',
            description: 'Perform case-insensitive search',
          },
        };

      case 'glob':
        return {
          pattern: {
            type: 'string',
            description: 'File pattern to match (supports wildcards)',
          },
          path: {
            type: 'string',
            description: 'Base directory to search in',
          },
        };

      case 'write':
        return {
          path: {
            type: 'string',
            description: 'File path to write to',
          },
          content: {
            type: 'string',
            description: 'Content to write to the file',
          },
          append: {
            type: 'boolean',
            description: 'Append to file instead of overwriting',
          },
        };

      case 'edit':
        return {
          path: {
            type: 'string',
            description: 'File path to edit',
          },
          operation: {
            type: 'string',
            enum: ['replace', 'insert', 'delete'],
            description: 'Type of edit operation',
          },
          content: {
            type: 'string',
            description: 'New content for replace/insert operations',
          },
          lineNumber: {
            type: 'number',
            description: 'Line number for insert/delete operations',
          },
          searchText: {
            type: 'string',
            description: 'Text to search for in replace operations',
          },
        };

      case 'web':
        return {
          query: {
            type: 'string',
            description: 'Search query for web search',
          },
          url: {
            type: 'string',
            description: 'URL to fetch content from',
          },
          operation: {
            type: 'string',
            enum: ['search', 'fetch'],
            description: 'Web operation to perform',
          },
        };

      default:
        return {
          args: {
            type: 'object',
            description: 'Tool arguments',
          },
        };
    }
  }

  private getRequiredParametersForTool(tool: Tool): string[] {
    switch (tool.name) {
      case 'bash':
        return ['command'];
      case 'grep':
        return ['pattern', 'path'];
      case 'glob':
        return ['pattern'];
      case 'write':
        return ['path', 'content'];
      case 'edit':
        return ['path', 'operation'];
      case 'web':
        return ['operation'];
      default:
        return [];
    }
  }

  // Tool documentation methods
  getToolDocumentation(toolName: string): string {
    const tool = this.getTool(toolName);
    if (!tool) {
      return `Tool "${toolName}" not found.`;
    }

    return this.generateToolDocumentation(tool);
  }

  getAllToolsDocumentation(): string {
    const docs = this.getAllTools().map(tool => this.generateToolDocumentation(tool));
    return docs.join('\n\n---\n\n');
  }

  private generateToolDocumentation(tool: Tool): string {
    const functionDef = this.toolToFunctionDefinition(tool);
    
    return `# ${tool.name.toUpperCase()} Tool

**Category:** ${tool.category}
**Description:** ${tool.description}

## Parameters:
${Object.entries(functionDef.parameters.properties)
  .map(([name, prop]) => {
    const required = functionDef.parameters.required?.includes(name) ? ' (required)' : ' (optional)';
    const enumValues = (prop as any).enum ? ` [${(prop as any).enum.join(', ')}]` : '';
    return `- **${name}**${required}: ${(prop as any).description}${enumValues}`;
  })
  .join('\n')}

## Usage Examples:
${this.getToolUsageExamples(tool.name)}`;
  }

  private getToolUsageExamples(toolName: string): string {
    // Return usage examples for each tool
    switch (toolName) {
      case 'bash':
        return `- List files: {"command": "ls -la"}
- Check system info: {"command": "uname -a"}
- Run with specific directory: {"command": "pwd", "workingDirectory": "/tmp"}`;

      case 'grep':
        return `- Search for text: {"pattern": "function", "path": "./src", "recursive": true}
- Case-insensitive search: {"pattern": "TODO", "path": ".", "recursive": true, "caseInsensitive": true}`;

      case 'glob':
        return `- Find TypeScript files: {"pattern": "*.ts", "path": "./src"}
- Find all JavaScript files: {"pattern": "**/*.js", "path": "."}`;

      case 'write':
        return `- Create new file: {"path": "example.txt", "content": "Hello World"}
- Append to file: {"path": "log.txt", "content": "New entry", "append": true}`;

      case 'edit':
        return `- Replace text: {"path": "file.txt", "operation": "replace", "searchText": "old", "content": "new"}
- Insert line: {"path": "file.txt", "operation": "insert", "lineNumber": 5, "content": "new line"}`;

      case 'web':
        return `- Search web: {"operation": "search", "query": "TypeScript best practices"}
- Fetch webpage: {"operation": "fetch", "url": "https://example.com"}`;

      default:
        return 'No examples available.';
    }
  }
}
