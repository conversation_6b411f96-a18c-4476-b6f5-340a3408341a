import axios, { AxiosInstance } from 'axios';
import { Tool, FunctionResult } from '../../types/index.js';

export class WebTool implements Tool {
  public readonly name = 'web';
  public readonly description = 'For retrieving and fetching real-time and up-to-date information from the internet using DuckDuckGo search API and direct URL fetching capabilities';
  public readonly category = 'web' as const;

  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      timeout: 15000,
      headers: {
        'User-Agent': 'Arien-AI-CLI/1.0.0 (https://github.com/arien-ai/cli)',
      },
    });
  }

  async execute(args: Record<string, unknown>): Promise<FunctionResult> {
    try {
      const { operation, query, url, maxResults = 5 } = args;

      if (typeof operation !== 'string') {
        return {
          success: false,
          error: 'Operation must be specified (search or fetch)',
        };
      }

      switch (operation) {
        case 'search':
          return await this.searchWeb(query as string, maxResults as number);
        case 'fetch':
          return await this.fetchUrl(url as string);
        default:
          return {
            success: false,
            error: `Unknown operation: ${operation}`,
          };
      }
    } catch (error: any) {
      return {
        success: false,
        error: `Web operation failed: ${error.message}`,
      };
    }
  }

  validate(args: Record<string, unknown>): boolean {
    const { operation, query, url } = args;
    
    if (typeof operation !== 'string') {
      return false;
    }

    switch (operation) {
      case 'search':
        return typeof query === 'string' && query.trim().length > 0;
      case 'fetch':
        return typeof url === 'string' && this.isValidUrl(url);
      default:
        return false;
    }
  }

  private async searchWeb(query: string, maxResults: number): Promise<FunctionResult> {
    try {
      // Using DuckDuckGo Instant Answer API
      const searchUrl = 'https://api.duckduckgo.com/';
      const params = {
        q: query,
        format: 'json',
        no_html: '1',
        skip_disambig: '1',
      };

      const response = await this.client.get(searchUrl, { params });
      const data = response.data;

      // Parse DuckDuckGo response
      const results = this.parseDuckDuckGoResponse(data, maxResults);

      return {
        success: true,
        result: {
          query,
          results,
          totalResults: results.length,
          source: 'DuckDuckGo',
        },
        output: results.length > 0 
          ? `Found ${results.length} results for "${query}"`
          : `No results found for "${query}"`,
      };
    } catch (error: any) {
      // Fallback to a simple web search simulation
      return {
        success: false,
        error: `Search failed: ${error.message}. Note: This tool requires internet access and may be limited by API availability.`,
      };
    }
  }

  private async fetchUrl(url: string): Promise<FunctionResult> {
    try {
      if (!this.isValidUrl(url)) {
        return {
          success: false,
          error: 'Invalid URL format',
        };
      }

      const response = await this.client.get(url, {
        maxRedirects: 5,
        validateStatus: (status) => status < 400,
      });

      const contentType = response.headers['content-type'] || '';
      const isTextContent = contentType.includes('text/') || 
                           contentType.includes('application/json') ||
                           contentType.includes('application/xml');

      if (!isTextContent) {
        return {
          success: false,
          error: 'URL does not contain text content',
        };
      }

      // Extract meaningful content (basic HTML parsing)
      let content = response.data;
      if (contentType.includes('text/html')) {
        content = this.extractTextFromHtml(content);
      }

      return {
        success: true,
        result: {
          url,
          contentType,
          content: content.substring(0, 10000), // Limit content size
          contentLength: content.length,
          statusCode: response.status,
        },
        output: `Successfully fetched content from ${url}`,
      };
    } catch (error: any) {
      const status = error.response?.status;
      const statusText = error.response?.statusText;
      
      return {
        success: false,
        error: `Failed to fetch URL: ${error.message}${status ? ` (${status} ${statusText})` : ''}`,
      };
    }
  }

  private parseDuckDuckGoResponse(data: any, maxResults: number): Array<{
    title: string;
    snippet: string;
    url: string;
    source: string;
  }> {
    const results: Array<{
      title: string;
      snippet: string;
      url: string;
      source: string;
    }> = [];

    // Abstract (main result)
    if (data.Abstract && data.AbstractURL) {
      results.push({
        title: data.Heading || 'Main Result',
        snippet: data.Abstract,
        url: data.AbstractURL,
        source: data.AbstractSource || 'DuckDuckGo',
      });
    }

    // Related topics
    if (data.RelatedTopics && Array.isArray(data.RelatedTopics)) {
      for (const topic of data.RelatedTopics.slice(0, maxResults - results.length)) {
        if (topic.Text && topic.FirstURL) {
          results.push({
            title: topic.Text.split(' - ')[0] || 'Related Topic',
            snippet: topic.Text,
            url: topic.FirstURL,
            source: 'DuckDuckGo Related',
          });
        }
      }
    }

    // Answer (if available)
    if (data.Answer && data.AnswerType) {
      results.unshift({
        title: `${data.AnswerType} Answer`,
        snippet: data.Answer,
        url: data.AbstractURL || '',
        source: 'DuckDuckGo Answer',
      });
    }

    return results.slice(0, maxResults);
  }

  private extractTextFromHtml(html: string): string {
    // Basic HTML tag removal (not a full parser)
    return html
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<[^>]*>/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }

  getDocumentation(): string {
    return `
# WEB Tool

**Category:** Web
**Description:** For retrieving and fetching real-time and up-to-date information from the internet

## Primary Use Cases:
- Search for current information and news
- Fetch content from specific websites
- Research topics and gather data
- Verify facts and get latest updates
- Access documentation and resources

## When NOT to Use:
- For local file operations (use file tools instead)
- When offline or without internet access
- For downloading large files or media
- For operations requiring authentication

## Operations:
### search
- **query** (required): Search terms to look for
- **maxResults** (optional): Maximum number of results (default: 5)

### fetch
- **url** (required): URL to fetch content from

## Best Practices:
- Use specific search terms for better results
- Verify URLs before fetching
- Be mindful of rate limits and API availability
- Handle network errors gracefully
- Respect website terms of service

## Performance Considerations:
- 15-second timeout for all requests
- Content limited to 10KB for fetched pages
- Maximum 5 redirects followed
- Text content only (no binary files)

## Privacy Considerations:
- Uses DuckDuckGo for privacy-focused search
- No tracking or personal data collection
- Respects website robots.txt when possible
- User-Agent identifies as Arien AI CLI

## Integration Notes:
- Perfect for research and fact-checking
- Combine with write tool to save fetched content
- Use with grep tool to search saved web content
- Complement local tools with external data

## Examples:
\`\`\`json
{"operation": "search", "query": "TypeScript best practices 2024"}
{"operation": "search", "query": "latest Node.js version", "maxResults": 3}
{"operation": "fetch", "url": "https://api.github.com/repos/microsoft/typescript"}
\`\`\`

## Error Handling:
- Graceful handling of network timeouts
- Clear error messages for invalid URLs
- Fallback behavior for API limitations
- Status code reporting for HTTP errors

## Rate Limits:
- Respects DuckDuckGo API limitations
- Built-in delays prevent overwhelming servers
- Automatic retry for temporary failures
- Clear feedback when limits are reached
`;
  }
}
