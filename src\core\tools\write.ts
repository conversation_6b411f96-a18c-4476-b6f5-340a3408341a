import { writeFile, appendFile, mkdir, access, constants } from 'fs/promises';
import { dirname } from 'path';
import { Tool, FunctionResult } from '../../types/index.js';

export class WriteTool implements Tool {
  public readonly name = 'write';
  public readonly description = 'File writing tool that creates or updates files in the filesystem, allowing you to save or modify text content with automatic directory creation and backup options';
  public readonly category = 'file' as const;

  async execute(args: Record<string, unknown>): Promise<FunctionResult> {
    try {
      const { 
        path, 
        content, 
        append = false, 
        createDirectories = true,
        backup = false,
        encoding = 'utf8'
      } = args;

      if (typeof path !== 'string' || typeof content !== 'string') {
        return {
          success: false,
          error: 'Path and content must be strings',
        };
      }

      // Validate path safety
      if (!this.isPathSafe(path)) {
        return {
          success: false,
          error: 'Path is not safe or contains invalid characters',
        };
      }

      // Create directories if needed
      if (createDirectories) {
        const dir = dirname(path);
        try {
          await mkdir(dir, { recursive: true });
        } catch (error: any) {
          if (error.code !== 'EEXIST') {
            return {
              success: false,
              error: `Failed to create directory: ${error.message}`,
            };
          }
        }
      }

      // Create backup if requested and file exists
      let backupPath: string | undefined;
      if (backup) {
        try {
          await access(path, constants.F_OK);
          backupPath = `${path}.backup.${Date.now()}`;
          const existingContent = await import('fs/promises').then(fs => fs.readFile(path, 'utf8'));
          await writeFile(backupPath, existingContent, encoding as BufferEncoding);
        } catch {
          // File doesn't exist, no backup needed
        }
      }

      // Write or append content
      const operation = append ? appendFile : writeFile;
      await operation(path, content, encoding as BufferEncoding);

      // Get file stats for response
      const stats = await import('fs/promises').then(fs => fs.stat(path));

      return {
        success: true,
        result: {
          path,
          operation: append ? 'append' : 'write',
          bytesWritten: Buffer.byteLength(content, encoding as BufferEncoding),
          fileSize: stats.size,
          modifiedTime: stats.mtime,
          backupPath,
        },
        output: `Successfully ${append ? 'appended to' : 'wrote'} file: ${path}`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: `File write failed: ${error.message}`,
      };
    }
  }

  validate(args: Record<string, unknown>): boolean {
    return (
      typeof args.path === 'string' &&
      typeof args.content === 'string' &&
      args.path.trim().length > 0
    );
  }

  private isPathSafe(path: string): boolean {
    // Basic path safety checks
    const normalizedPath = path.replace(/\\/g, '/');
    
    // Reject paths that try to escape the current directory structure
    if (normalizedPath.includes('../') || normalizedPath.startsWith('/')) {
      return false;
    }

    // Reject paths with null bytes or other dangerous characters
    if (normalizedPath.includes('\0') || normalizedPath.includes('\r') || normalizedPath.includes('\n')) {
      return false;
    }

    // Reject system/sensitive file patterns
    const dangerousPatterns = [
      /\/etc\//,
      /\/bin\//,
      /\/usr\/bin\//,
      /\/sbin\//,
      /\/boot\//,
      /\/sys\//,
      /\/proc\//,
      /\/dev\//,
      /passwd$/,
      /shadow$/,
      /sudoers$/,
    ];

    return !dangerousPatterns.some(pattern => pattern.test(normalizedPath.toLowerCase()));
  }

  getDocumentation(): string {
    return `
# WRITE Tool

**Category:** File
**Description:** File writing tool that creates or updates files in the filesystem

## Primary Use Cases:
- Create new files with content
- Update existing files by overwriting
- Append content to existing files
- Save generated code, configurations, or documentation
- Create log files and data exports

## When NOT to Use:
- For complex file edits (use edit tool for precise modifications)
- For binary file operations
- When you need to preserve file permissions/metadata exactly
- For moving or renaming files (use bash tool with mv command)

## Parameters:
- **path** (required): File path to write to
- **content** (required): Content to write to the file
- **append** (optional): Append to file instead of overwriting (default: false)
- **createDirectories** (optional): Create parent directories if needed (default: true)
- **backup** (optional): Create backup of existing file (default: false)
- **encoding** (optional): File encoding (default: 'utf8')

## Best Practices:
- Always validate file paths for security
- Use backup option for important file modifications
- Consider file size limits for large content
- Use appropriate encoding for file type
- Handle directory creation gracefully

## Performance Considerations:
- Large files may take time to write
- Backup operations double the write time
- Directory creation adds overhead
- Atomic write operations prevent corruption

## Integration Notes:
- Works well after glob tool for batch file creation
- Combine with grep tool to create filtered content files
- Use with edit tool for complex file modifications
- Backup feature integrates with version control workflows

## Security Features:
- Path validation prevents directory traversal
- Rejects dangerous system file paths
- Validates against null bytes and control characters
- Automatic directory creation is sandboxed

## Examples:
\`\`\`json
{"path": "example.txt", "content": "Hello World"}
{"path": "log.txt", "content": "New entry\\n", "append": true}
{"path": "config/app.json", "content": "{}", "createDirectories": true}
{"path": "important.txt", "content": "Updated", "backup": true}
\`\`\`

## Error Handling:
- Graceful handling of permission errors
- Clear error messages for invalid paths
- Automatic recovery from directory creation failures
- Backup failure doesn't prevent main operation
`;
  }
}
