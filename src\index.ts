#!/usr/bin/env node

import { Command } from 'commander';
import { <PERSON>enAI } from './core/arien-ai.js';
import { ConfigManager } from './core/config/manager.js';
import chalk from 'chalk';

const program = new Command();

program
  .name('arien')
  .description('Sophisticated AI-powered CLI terminal system')
  .version('1.0.0');

program
  .command('chat')
  .description('Start interactive chat session')
  .option('-p, --provider <provider>', 'AI provider to use')
  .option('-m, --model <model>', 'Model to use')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async (options) => {
    try {
      const arien = new ArienAI();
      await arien.start(options);
    } catch (error) {
      console.error(chalk.red('Failed to start Arien AI:'), (error as Error).message);
      process.exit(1);
    }
  });

program
  .command('config')
  .description('Manage configuration')
  .option('-l, --list', 'List all configuration')
  .option('-s, --set <key=value>', 'Set configuration value')
  .option('-g, --get <key>', 'Get configuration value')
  .option('-r, --reset', 'Reset configuration to defaults')
  .action(async (options) => {
    const configManager = new ConfigManager();
    
    try {
      if (options.list) {
        const config = configManager.export();
        console.log(JSON.stringify(config, null, 2));
      } else if (options.set) {
        const [key, value] = options.set.split('=');
        if (!key || value === undefined) {
          console.error(chalk.red('Invalid format. Use: key=value'));
          process.exit(1);
        }
        // Handle setting configuration
        console.log(chalk.green(`Set ${key} = ${value}`));
      } else if (options.get) {
        // Handle getting configuration
        console.log(chalk.blue(`Getting ${options.get}`));
      } else if (options.reset) {
        configManager.reset();
        console.log(chalk.green('Configuration reset to defaults'));
      } else {
        console.log(chalk.yellow('Use --help to see available options'));
      }
    } catch (error) {
      console.error(chalk.red('Configuration error:'), (error as Error).message);
      process.exit(1);
    }
  });

program
  .command('providers')
  .description('Manage AI providers')
  .option('-l, --list', 'List configured providers')
  .option('-a, --add <name>', 'Add new provider')
  .option('-r, --remove <name>', 'Remove provider')
  .option('-t, --test <name>', 'Test provider connection')
  .action(async (options) => {
    const configManager = new ConfigManager();
    
    try {
      if (options.list) {
        const providers = configManager.getAllProviders();
        const current = configManager.getCurrentProvider();
        
        console.log(chalk.cyan('Configured Providers:'));
        Object.entries(providers).forEach(([name, config]) => {
          const marker = name === current ? chalk.green('* ') : '  ';
          console.log(`${marker}${name}: ${config.model} (${config.baseUrl || 'default'})`);
        });
      } else if (options.add) {
        console.log(chalk.blue(`Adding provider: ${options.add}`));
        // This would trigger the interactive setup
        const arien = new ArienAI();
        await arien.setupProvider(options.add);
      } else if (options.remove) {
        configManager.deleteProvider(options.remove);
        console.log(chalk.green(`Removed provider: ${options.remove}`));
      } else if (options.test) {
        console.log(chalk.blue(`Testing provider: ${options.test}`));
        // Test provider connection
        const arien = new ArienAI();
        await arien.testProvider(options.test);
      } else {
        console.log(chalk.yellow('Use --help to see available options'));
      }
    } catch (error) {
      console.error(chalk.red('Provider error:'), (error as Error).message);
      process.exit(1);
    }
  });

program
  .command('tools')
  .description('List available tools and their documentation')
  .option('-l, --list', 'List all tools')
  .option('-d, --docs <tool>', 'Show documentation for specific tool')
  .option('-a, --all-docs', 'Show documentation for all tools')
  .action(async (options) => {
    try {
      const arien = new ArienAI();
      
      if (options.list) {
        await arien.listTools();
      } else if (options.docs) {
        await arien.showToolDocs(options.docs);
      } else if (options.allDocs) {
        await arien.showAllToolDocs();
      } else {
        console.log(chalk.yellow('Use --help to see available options'));
      }
    } catch (error) {
      console.error(chalk.red('Tools error:'), (error as Error).message);
      process.exit(1);
    }
  });

program
  .command('setup')
  .description('Interactive setup wizard')
  .action(async () => {
    try {
      const arien = new ArienAI();
      await arien.runSetupWizard();
    } catch (error) {
      console.error(chalk.red('Setup failed:'), (error as Error).message);
      process.exit(1);
    }
  });

// Default command (chat)
program
  .action(async (options) => {
    try {
      const arien = new ArienAI();
      await arien.start(options);
    } catch (error) {
      console.error(chalk.red('Failed to start Arien AI:'), (error as Error).message);
      process.exit(1);
    }
  });

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Uncaught Exception:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('Unhandled Rejection at:'), promise, chalk.red('reason:'), reason);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log(chalk.cyan('\nGracefully shutting down...'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.cyan('\nReceived SIGTERM, shutting down...'));
  process.exit(0);
});

program.parse();
