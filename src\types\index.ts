// Core AI Provider Types
export interface AIProvider {
  name: string;
  type: 'api' | 'local';
  models: string[];
  authenticate(config: ProviderConfig): Promise<boolean>;
  chat(messages: ChatMessage[], options?: ChatOptions): Promise<ChatResponse>;
  streamChat(messages: ChatMessage[], options?: ChatOptions): AsyncGenerator<ChatChunk>;
  callFunction(functionCall: FunctionCall): Promise<FunctionResult>;
  getAvailableModels?(): Promise<string[]>;
}

export interface ProviderConfig {
  apiKey?: string;
  baseUrl?: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  [key: string]: unknown;
}

// Chat Types
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'function';
  content: string;
  name?: string;
  functionCall?: FunctionCall;
}

export interface ChatOptions {
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  functions?: FunctionDefinition[];
  functionCall?: 'auto' | 'none' | { name: string };
}

export interface ChatResponse {
  content: string;
  functionCall?: FunctionCall;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  } | undefined;
}

export interface ChatChunk {
  content?: string;
  functionCall?: Partial<FunctionCall>;
  done: boolean;
}

// Function Calling Types
export interface FunctionDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, {
      type: string;
      description: string;
      enum?: string[];
    }>;
    required?: string[];
  };
}

export interface FunctionCall {
  name: string;
  arguments: string | Record<string, unknown>;
}

export interface FunctionResult {
  success: boolean;
  result?: unknown;
  error?: string;
  output?: string;
}

// CLI Types
export interface CLIConfig {
  provider: string;
  model: string;
  apiKey?: string | undefined;
  baseUrl?: string | undefined;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  autoSave: boolean;
  theme: 'light' | 'dark' | 'auto';
}

export interface CommandContext {
  config: CLIConfig;
  provider: AIProvider;
  history: ChatMessage[];
  isStreaming: boolean;
  currentCommand?: string;
}

// Tool Types
export interface Tool {
  name: string;
  description: string;
  category: 'system' | 'file' | 'web' | 'development';
  execute(args: Record<string, unknown>): Promise<FunctionResult>;
  validate?(args: Record<string, unknown>): boolean;
}

// UI Types
export interface UIState {
  isAuthenticated: boolean;
  currentProvider: string;
  currentModel: string;
  isStreaming: boolean;
  showCommandPalette: boolean;
  commandPaletteQuery: string;
  selectedCommandIndex: number;
  availableCommands: SlashCommand[];
}

export interface SlashCommand {
  name: string;
  description: string;
  usage: string;
  handler: (args: string[]) => Promise<void>;
}

// Error Types
export interface ArienError extends Error {
  code: string;
  category: 'auth' | 'network' | 'config' | 'tool' | 'ai' | 'system';
  retryable: boolean;
  context?: Record<string, unknown> | undefined;
}

// Retry Logic Types
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryableErrors: string[];
}

// Self-Learning Types
export interface PromptMetrics {
  promptId: string;
  timestamp: Date;
  taskType: string;
  success: boolean;
  responseQuality: number;
  executionTime: number;
  tokensUsed: number;
  userSatisfaction?: number;
}

export interface PromptTemplate {
  id: string;
  name: string;
  template: string;
  taskType: string;
  version: number;
  metrics: PromptMetrics[];
  effectiveness: number;
}

// Animation Types
export interface AnimationFrame {
  frame: string;
  duration: number;
}

export interface LoadingAnimation {
  name: string;
  frames: AnimationFrame[];
  interval: number;
}

// Configuration Storage Types
export interface StoredConfig {
  providers: Record<string, ProviderConfig>;
  currentProvider: string;
  userPreferences: {
    theme: string;
    autoSave: boolean;
    notifications: boolean;
    verboseLogging: boolean;
  };
  promptTemplates: PromptTemplate[];
  lastUpdated: Date;
}
