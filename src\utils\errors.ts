import { ArienError } from '../types/index.js';

export class ArienErrorImpl extends Error implements ArienError {
  public readonly code: string;
  public readonly category: 'auth' | 'network' | 'config' | 'tool' | 'ai' | 'system';
  public readonly retryable: boolean;
  public readonly context?: Record<string, unknown> | undefined;

  constructor(
    message: string,
    code: string,
    category: ArienError['category'],
    retryable = false,
    context?: Record<string, unknown> | undefined
  ) {
    super(message);
    this.name = 'ArienError';
    this.code = code;
    this.category = category;
    this.retryable = retryable;
    this.context = context;
  }
}

export const ErrorCodes = {
  // Authentication Errors
  AUTH_INVALID_API_KEY: 'AUTH_INVALID_API_KEY',
  AUTH_PROVIDER_UNAVAILABLE: 'AUTH_PROVIDER_UNAVAILABLE',
  AUTH_RATE_LIMITED: 'AUTH_RATE_LIMITED',

  // Network Errors
  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
  NETWORK_CONNECTION_FAILED: 'NETWORK_CONNECTION_FAILED',
  NETWORK_RATE_LIMITED: 'NETWORK_RATE_LIMITED',

  // Configuration Errors
  CONFIG_INVALID: 'CONFIG_INVALID',
  CONFIG_MISSING_REQUIRED: 'CONFIG_MISSING_REQUIRED',
  CONFIG_SAVE_FAILED: 'CONFIG_SAVE_FAILED',

  // Tool Errors
  TOOL_EXECUTION_FAILED: 'TOOL_EXECUTION_FAILED',
  TOOL_INVALID_ARGS: 'TOOL_INVALID_ARGS',
  TOOL_PERMISSION_DENIED: 'TOOL_PERMISSION_DENIED',

  // AI Errors
  AI_MODEL_UNAVAILABLE: 'AI_MODEL_UNAVAILABLE',
  AI_RESPONSE_INVALID: 'AI_RESPONSE_INVALID',
  AI_FUNCTION_CALL_FAILED: 'AI_FUNCTION_CALL_FAILED',

  // System Errors
  SYSTEM_RESOURCE_UNAVAILABLE: 'SYSTEM_RESOURCE_UNAVAILABLE',
  SYSTEM_PERMISSION_DENIED: 'SYSTEM_PERMISSION_DENIED',
  SYSTEM_UNEXPECTED: 'SYSTEM_UNEXPECTED',
} as const;

export function createError(
  message: string,
  code: keyof typeof ErrorCodes,
  category: ArienError['category'],
  retryable = false,
  context?: Record<string, unknown>
): ArienError {
  return new ArienErrorImpl(message, ErrorCodes[code], category, retryable, context);
}

export function isRetryableError(error: Error): boolean {
  if (error instanceof ArienErrorImpl) {
    return error.retryable;
  }

  // Check for common retryable error patterns
  const retryablePatterns = [
    /timeout/i,
    /rate limit/i,
    /too many requests/i,
    /service unavailable/i,
    /temporary/i,
    /ECONNRESET/i,
    /ENOTFOUND/i,
  ];

  return retryablePatterns.some(pattern => pattern.test(error.message));
}

export function categorizeError(error: Error): ArienError['category'] {
  if (error instanceof ArienErrorImpl) {
    return error.category;
  }

  const message = error.message.toLowerCase();

  if (message.includes('auth') || message.includes('unauthorized') || message.includes('api key')) {
    return 'auth';
  }

  if (
    message.includes('network') ||
    message.includes('timeout') ||
    message.includes('connection')
  ) {
    return 'network';
  }

  if (message.includes('config') || message.includes('invalid') || message.includes('missing')) {
    return 'config';
  }

  if (message.includes('tool') || message.includes('function') || message.includes('execution')) {
    return 'tool';
  }

  if (message.includes('model') || message.includes('ai') || message.includes('response')) {
    return 'ai';
  }

  return 'system';
}
