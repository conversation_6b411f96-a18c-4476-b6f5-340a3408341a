import { RetryConfig, ArienError } from '../types/index.js';
import { isRetryableError, createError, ErrorCodes } from './errors.js';

export class RetryManager {
  private config: RetryConfig;

  constructor(config: Partial<RetryConfig> = {}) {
    this.config = {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffFactor: 2,
      retryableErrors: [
        ErrorCodes.NETWORK_TIMEOUT,
        ErrorCodes.NETWORK_CONNECTION_FAILED,
        ErrorCodes.NETWORK_RATE_LIMITED,
        ErrorCodes.AUTH_RATE_LIMITED,
        ErrorCodes.AI_MODEL_UNAVAILABLE,
      ],
      ...config,
    };
  }

  async execute<T>(
    operation: () => Promise<T>,
    context?: Record<string, unknown>
  ): Promise<T> {
    let lastError: Error;
    let attempt = 0;

    while (attempt < this.config.maxAttempts) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        attempt++;

        if (!this.shouldRetry(error as Error, attempt)) {
          break;
        }

        const delay = this.calculateDelay(attempt);
        console.warn(
          `Attempt ${attempt} failed: ${lastError.message}. Retrying in ${delay}ms...`
        );

        await this.sleep(delay);
      }
    }

    throw createError(
      `Operation failed after ${attempt} attempts: ${lastError!.message}`,
      'SYSTEM_UNEXPECTED',
      'system',
      false,
      { ...context, attempts: attempt, lastError: lastError!.message }
    );
  }

  private shouldRetry(error: Error, attempt: number): boolean {
    if (attempt >= this.config.maxAttempts) {
      return false;
    }

    // Check if error is retryable by type
    if (!isRetryableError(error)) {
      return false;
    }

    // Check if error code is in retryable list
    if (error instanceof Error && 'code' in error) {
      const errorCode = (error as ArienError).code;
      if (errorCode && !this.config.retryableErrors.includes(errorCode)) {
        return false;
      }
    }

    return true;
  }

  private calculateDelay(attempt: number): number {
    const delay = this.config.baseDelay * Math.pow(this.config.backoffFactor, attempt - 1);
    const jitter = Math.random() * 0.1 * delay; // Add 10% jitter
    return Math.min(delay + jitter, this.config.maxDelay);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  updateConfig(newConfig: Partial<RetryConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  getConfig(): RetryConfig {
    return { ...this.config };
  }
}

// Utility function for quick retry operations
export async function withRetry<T>(
  operation: () => Promise<T>,
  config?: Partial<RetryConfig>,
  context?: Record<string, unknown>
): Promise<T> {
  const retryManager = new RetryManager(config);
  return retryManager.execute(operation, context);
}

// Specialized retry for network operations
export async function withNetworkRetry<T>(
  operation: () => Promise<T>,
  context?: Record<string, unknown>
): Promise<T> {
  return withRetry(
    operation,
    {
      maxAttempts: 5,
      baseDelay: 2000,
      maxDelay: 60000,
      backoffFactor: 2.5,
    },
    context
  );
}

// Specialized retry for AI operations
export async function withAIRetry<T>(
  operation: () => Promise<T>,
  context?: Record<string, unknown>
): Promise<T> {
  return withRetry(
    operation,
    {
      maxAttempts: 3,
      baseDelay: 1500,
      maxDelay: 15000,
      backoffFactor: 2,
    },
    context
  );
}
