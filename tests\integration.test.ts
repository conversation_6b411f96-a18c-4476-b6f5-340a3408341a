import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ArienAI } from '../src/core/arien-ai.js';
import { ConfigManager } from '../src/core/config/manager.js';
import { SystemPromptManager } from '../src/core/prompts/system-prompt.js';
import { ToolManager } from '../src/core/tools/index.js';
import { CLIInterface } from '../src/components/cli-interface.js';

// Mock external dependencies
vi.mock('../src/components/cli-interface.js');
vi.mock('inquirer');

describe('Integration Tests', () => {
  let arienAI: ArienAI;
  let configManager: ConfigManager;
  let systemPromptManager: SystemPromptManager;
  let toolManager: ToolManager;

  beforeEach(() => {
    vi.clearAllMocks();
    configManager = new ConfigManager();
    toolManager = new ToolManager();
    systemPromptManager = new SystemPromptManager(toolManager);
  });

  describe('System Prompt Manager', () => {
    it('should generate comprehensive system prompt', () => {
      const prompt = systemPromptManager.generateSystemPrompt();
      
      expect(prompt).toContain('ARIEN AI');
      expect(prompt).toContain('AUTONOMOUS BEHAVIOR PRINCIPLES');
      expect(prompt).toContain('NEVER GIVE UP LOGIC');
      expect(prompt).toContain('CRITICAL OPERATIONAL INSTRUCTIONS');
      expect(prompt).toContain('WORKFLOW STRATEGIES');
      expect(prompt).toContain('AVAILABLE TOOLS');
    });

    it('should record and analyze performance metrics', () => {
      systemPromptManager.recordPromptPerformance('test_prompt', true, 1000, 0.9);
      systemPromptManager.recordPromptPerformance('test_prompt', false, 2000, 0.3);
      
      const report = systemPromptManager.getPerformanceReport();
      expect(report).toContain('SYSTEM PERFORMANCE REPORT');
      expect(report).toContain('Success Metrics');
      expect(report).toContain('Recommendations');
    });

    it('should generate improved prompts based on performance', () => {
      // Record some performance data
      systemPromptManager.recordPromptPerformance('successful_strategy', true, 500, 0.95);
      systemPromptManager.recordPromptPerformance('failed_strategy', false, 3000, 0.2);
      
      const improvedPrompt = systemPromptManager.generateImprovedPrompt();
      expect(improvedPrompt).toContain('PERFORMANCE-OPTIMIZED STRATEGIES');
    });
  });

  describe('Tool Manager Integration', () => {
    it('should provide comprehensive tool documentation', () => {
      const allDocs = toolManager.getAllToolsDocumentation();
      
      expect(allDocs).toContain('BASH Tool');
      expect(allDocs).toContain('GREP Tool');
      expect(allDocs).toContain('GLOB Tool');
      expect(allDocs).toContain('WRITE Tool');
      expect(allDocs).toContain('EDIT Tool');
      expect(allDocs).toContain('WEB Tool');
    });

    it('should generate function definitions for all tools', () => {
      const functionDefs = toolManager.getFunctionDefinitions();
      
      expect(functionDefs).toHaveLength(6);
      expect(functionDefs.map(f => f.name)).toEqual([
        'bash', 'grep', 'glob', 'write', 'edit', 'web'
      ]);
      
      // Check that each function has proper parameters
      functionDefs.forEach(func => {
        expect(func.parameters.type).toBe('object');
        expect(func.parameters.properties).toBeDefined();
        expect(func.parameters.required).toBeDefined();
      });
    });

    it('should execute tools with proper error handling', async () => {
      // Test successful execution
      const result = await toolManager.executeTool('bash', { command: 'echo "test"' });
      expect(result.success).toBe(true);
      
      // Test invalid tool
      const invalidResult = await toolManager.executeTool('nonexistent', {});
      expect(invalidResult.success).toBe(false);
      expect(invalidResult.error).toContain('Tool "nonexistent" not found');
    });
  });

  describe('Configuration Management', () => {
    it('should validate configuration properly', () => {
      const validation = configManager.validateConfig();
      expect(validation).toHaveProperty('isValid');
      expect(validation).toHaveProperty('errors');
    });

    it('should manage providers correctly', async () => {
      const testConfig = {
        model: 'test-model',
        apiKey: 'test-key',
        baseUrl: 'http://test.com'
      };
      
      await configManager.saveProvider('test-provider', testConfig);
      const retrieved = configManager.getProvider('test-provider');
      
      expect(retrieved).toEqual(testConfig);
    });

    it('should export and import configuration', () => {
      const originalConfig = configManager.export();
      
      // Modify and import
      const modifiedConfig = { ...originalConfig, test: 'value' };
      configManager.import(modifiedConfig);
      
      const newConfig = configManager.export();
      expect(newConfig).toHaveProperty('test', 'value');
    });
  });

  describe('Error Handling and Retry Logic', () => {
    it('should handle errors gracefully', () => {
      // This would test the error handling in ArienAI
      // For now, we'll test that the error utilities work
      expect(() => {
        throw new Error('Test error');
      }).toThrow('Test error');
    });
  });

  describe('CLI Interface Features', () => {
    it('should initialize CLI interface properly', async () => {
      const cli = new CLIInterface();
      expect(cli).toBeDefined();
      
      // Test that it has the required methods
      expect(typeof cli.initialize).toBe('function');
      expect(typeof cli.displayMessage).toBe('function');
      expect(typeof cli.startStreaming).toBe('function');
      expect(typeof cli.stopStreaming).toBe('function');
    });
  });
});
