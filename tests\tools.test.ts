import { describe, it, expect, beforeEach } from 'vitest';
import { ToolManager } from '../src/core/tools/index.js';
import { BashTool } from '../src/core/tools/bash.js';
import { GrepTool } from '../src/core/tools/grep.js';
import { GlobTool } from '../src/core/tools/glob.js';
import { WriteTool } from '../src/core/tools/write.js';
import { EditTool } from '../src/core/tools/edit.js';
import { WebTool } from '../src/core/tools/web.js';

describe('ToolManager', () => {
  let toolManager: ToolManager;

  beforeEach(() => {
    toolManager = new ToolManager();
  });

  it('should register default tools', () => {
    const tools = toolManager.getAllTools();
    expect(tools).toHaveLength(6);
    
    const toolNames = tools.map(tool => tool.name);
    expect(toolNames).toContain('bash');
    expect(toolNames).toContain('grep');
    expect(toolNames).toContain('glob');
    expect(toolNames).toContain('write');
    expect(toolNames).toContain('edit');
    expect(toolNames).toContain('web');
  });

  it('should get tool by name', () => {
    const bashTool = toolManager.getTool('bash');
    expect(bashTool).toBeDefined();
    expect(bashTool?.name).toBe('bash');
  });

  it('should return undefined for non-existent tool', () => {
    const tool = toolManager.getTool('nonexistent');
    expect(tool).toBeUndefined();
  });

  it('should get tools by category', () => {
    const systemTools = toolManager.getToolsByCategory('system');
    expect(systemTools).toHaveLength(1);
    expect(systemTools[0].name).toBe('bash');

    const fileTools = toolManager.getToolsByCategory('file');
    expect(fileTools.length).toBeGreaterThan(0);
  });

  it('should generate function definitions', () => {
    const definitions = toolManager.getFunctionDefinitions();
    expect(definitions).toHaveLength(6);
    
    const bashDef = definitions.find(def => def.name === 'bash');
    expect(bashDef).toBeDefined();
    expect(bashDef?.description).toContain('bash commands');
    expect(bashDef?.parameters.properties.command).toBeDefined();
  });
});

describe('BashTool', () => {
  let bashTool: BashTool;

  beforeEach(() => {
    bashTool = new BashTool();
  });

  it('should validate arguments correctly', () => {
    expect(bashTool.validate({ command: 'ls -la' })).toBe(true);
    expect(bashTool.validate({ command: '' })).toBe(false);
    expect(bashTool.validate({})).toBe(false);
    expect(bashTool.validate({ command: 123 })).toBe(false);
  });

  it('should execute simple commands', async () => {
    const result = await bashTool.execute({ command: 'echo "hello world"' });
    expect(result.success).toBe(true);
    expect(result.output).toContain('hello world');
  });

  it('should reject dangerous commands', async () => {
    const result = await bashTool.execute({ command: 'rm -rf /' });
    expect(result.success).toBe(false);
    expect(result.error).toContain('security reasons');
  });

  it('should handle command failures', async () => {
    const result = await bashTool.execute({ command: 'nonexistentcommand12345' });
    expect(result.success).toBe(false);
    expect(result.error).toContain('Command failed');
  });
});

describe('WriteTool', () => {
  let writeTool: WriteTool;

  beforeEach(() => {
    writeTool = new WriteTool();
  });

  it('should validate arguments correctly', () => {
    expect(writeTool.validate({ path: 'test.txt', content: 'hello' })).toBe(true);
    expect(writeTool.validate({ path: '', content: 'hello' })).toBe(false);
    expect(writeTool.validate({ path: 'test.txt' })).toBe(false);
    expect(writeTool.validate({})).toBe(false);
  });

  it('should create files with content', async () => {
    const testPath = './test-output.txt';
    const testContent = 'Hello, World!';
    
    const result = await writeTool.execute({
      path: testPath,
      content: testContent,
    });

    expect(result.success).toBe(true);
    expect(result.output).toContain('Successfully wrote file');
    
    // Clean up
    try {
      await import('fs/promises').then(fs => fs.unlink(testPath));
    } catch {
      // File might not exist, ignore
    }
  });
});

describe('EditTool', () => {
  let editTool: EditTool;

  beforeEach(() => {
    editTool = new EditTool();
  });

  it('should validate arguments correctly', () => {
    expect(editTool.validate({ 
      path: 'test.txt', 
      operation: 'replace', 
      searchText: 'old', 
      replaceText: 'new' 
    })).toBe(true);
    
    expect(editTool.validate({ 
      path: 'test.txt', 
      operation: 'insert', 
      lineNumber: 1, 
      content: 'new line' 
    })).toBe(true);
    
    expect(editTool.validate({ 
      path: 'test.txt', 
      operation: 'delete', 
      lineNumber: 1 
    })).toBe(true);
    
    expect(editTool.validate({ 
      path: 'test.txt', 
      operation: 'create', 
      content: 'hello' 
    })).toBe(true);
    
    expect(editTool.validate({ path: 'test.txt' })).toBe(false);
    expect(editTool.validate({ operation: 'replace' })).toBe(false);
  });
});

describe('WebTool', () => {
  let webTool: WebTool;

  beforeEach(() => {
    webTool = new WebTool();
  });

  it('should validate arguments correctly', () => {
    expect(webTool.validate({ 
      operation: 'search', 
      query: 'test query' 
    })).toBe(true);
    
    expect(webTool.validate({ 
      operation: 'fetch', 
      url: 'https://example.com' 
    })).toBe(true);
    
    expect(webTool.validate({ operation: 'search' })).toBe(false);
    expect(webTool.validate({ operation: 'fetch' })).toBe(false);
    expect(webTool.validate({ operation: 'invalid' })).toBe(false);
  });

  it('should reject invalid URLs', () => {
    expect(webTool.validate({ 
      operation: 'fetch', 
      url: 'not-a-url' 
    })).toBe(false);
    
    expect(webTool.validate({ 
      operation: 'fetch', 
      url: 'ftp://example.com' 
    })).toBe(false);
  });
});

describe('Tool Integration', () => {
  let toolManager: ToolManager;

  beforeEach(() => {
    toolManager = new ToolManager();
  });

  it('should execute tools through manager', async () => {
    const result = await toolManager.executeTool('bash', { 
      command: 'echo "integration test"' 
    });
    
    expect(result.success).toBe(true);
    expect(result.output).toContain('integration test');
  });

  it('should handle non-existent tools', async () => {
    const result = await toolManager.executeTool('nonexistent', {});
    expect(result.success).toBe(false);
    expect(result.error).toContain('Tool "nonexistent" not found');
  });

  it('should handle invalid arguments', async () => {
    const result = await toolManager.executeTool('bash', { 
      command: 123 // Invalid type
    });
    
    expect(result.success).toBe(false);
    expect(result.error).toContain('Invalid arguments');
  });
});
