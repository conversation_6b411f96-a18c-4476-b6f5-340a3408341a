{"compilerOptions": {"target": "ES2023", "module": "Node16", "moduleResolution": "Node16", "lib": ["ES2023", "DOM"], "outDir": "./dist", "rootDir": "./src", "strict": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "useUnknownInCatchVariables": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/core/*": ["./core/*"], "@/components/*": ["./components/*"], "@/utils/*": ["./utils/*"], "@/types/*": ["./types/*"]}, "verbatimModuleSyntax": false, "allowImportingTsExtensions": false, "noEmitOnError": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests", "**/*.test.ts", "**/*.spec.ts"]}